/*!
 * ZUI: 下拉选择器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(e,t,i){"use strict";var o="zui.picker",r={},n={lang:null,remote:null,remoteConverter:null,remoteOnly:!1,onRemoteError:null,disableEmptySearch:!1,textKey:"text",valueKey:"value",keysKey:"keys",multi:"auto",formItem:"auto",list:null,allowSingleDeselect:null,showMultiSelectedOptions:!1,autoSelectFirst:!1,maxSelectedCount:0,maxListCount:50,hideEmptyTextOption:!0,searchValueKey:!0,emptyResultHint:null,hideOnWindowScroll:!0,inheritFormItemClasses:!1,emptySearchResultHint:null,accurateSearchHint:null,remoteErrorHint:null,deleteByBackspace:!0,disableScrollOnShow:!0,maxDropHeight:250,dropDirection:"auto",dropWidth:"100%",maxAutoDropWidth:450,multiValueSplitter:",",searchDelay:200,autoClearDrop:6e4,fixLabelFor:!0,hotkey:!0,onSelect:null,onDeselect:null,onBeforeChange:null,onChange:null,onReady:null,onNoResults:null,onShowingDrop:null,onHidingDrop:null,onShowedDrop:null,onHiddenDrop:null,valueMustInList:!0},s={zh_cn:{emptyResultHint:"没有可选项",emptySearchResultHint:"没有找到 “{0}”",accurateSearchHint:"请提供更多关键词缩小匹配范围",remoteErrorHint:"无法从服务器获取结果 - {0}"},zh_tw:{emptyResultHint:"沒有可選項",emptySearchResultHint:"沒有找到 “{0}”",accurateSearchHint:"請提供更多關鍵詞縮小匹配範圍",remoteErrorHint:"無法從服務器獲取結果 - {0}"},en:{emptyResultHint:"No options",emptySearchResultHint:'Cannot found "{0}"',accurateSearchHint:"Suggest to provide more keywords",remoteErrorHint:"Unable to get result from server: {0}"}},l=function(t,i){var r=this;r.name=o,r.$=e(t),r.id="pk_"+(r.$.attr("id")||e.zui.uuid()),i=r.options=e.extend({},l.DEFAULTS,this.$.data(),i);var n=e.zui.clientLang?e.zui.clientLang():"en",a=i.lang||n;r.lang=e.zui.getLangData?e.zui.getLangData(o,a,s):s[a]||s[n];var p,c,u=i.formItem,d='.form-item,input[type="hidden"],select,input[type="text"]';if(p="self"===u?r.$:"auto"!==u&&u?r.$.find(u):r.$.is(d)?r.$:r.$.find(d).first(),!p.length)return console.error&&console.error("Cannot found form item for picker.");if(p.is('input[type="hidden"]'))c="hidden";else if(p.is("select"))c="select";else{if(!p.is('input[type="text"]'))return console.error&&console.error("Unknown form type for picker.");c="text"}i.inheritFormItemClasses&&f.addClass(p.attr("class")),r.formType=c,r.$formItem=p.removeClass("picker").hide(),r.selfFormItem=p.is(r.$);var h=i.multi;h&&"auto"!==h||(h="select"===c&&"multiple"===p.attr("multiple")),h=!!h,r.multi=h;var v=i.list;v?r.setList("function"==typeof v?v({search:r.search,limit:i.maxListCount}):v,!0):"select"===c?r.updateFromSelect():r.setList([],!0);var f;f=!r.selfFormItem&&r.$.hasClass("picker")?r.$:e('<div class="picker" />').insertAfter(r.$),f.addClass("picker").toggleClass("picker-multi",h).toggleClass("picker-single",!h);var g=f.children(".picker-selections");g.length?g.empty():g=e('<div class="picker-selections" />');var m=r.id+"-search",y=e('<input autocomplete="off" id="'+m+'" type="text" class="picker-search">').appendTo(g);if(!h){var k=e('<div class="picker-selection picker-selection-single"><span class="picker-selection-text"></span></div>');i.allowSingleDeselect&&k.append('<span class="picker-selection-remove"></span>'),k.appendTo(g),r.$singleSelection=k}f.toggleClass("picker-input-empty",!y.val().length).append(g),r.$container=f,r.$selections=g,r.$search=y,r.search="";var L=i.placeholder;if(void 0===L&&(L=p.attr("placeholder")),"string"==typeof L&&L.length&&g.append(e('<div class="picker-placeholder" />').text(L)),i.placeholder=L,i.fixLabelFor){var S=p.attr("id");S&&e('label[for="'+S+'"]').attr("for",m)}var x=void 0!==i.defaultValue?i.defaultValue:p.val();if(r.setValue(x,!0),y.on("focus",function(){f.addClass("picker-focus"),r.showDropList()}).on("blur",function(){f.removeClass("picker-focus")}).on("input change",function(){var e=y.val();h&&y.width(14*e.length),f.toggleClass("picker-input-empty",!e.length),r.tryUpdateList(e)}),i.hotkey&&y.on("keydown",function(e){var t=e.key||e.which;if(r.dropListShowed){var o=r.activeValue,n="string"==typeof o;if("Enter"===t||13===t)n&&(r.select(o,h),h?(r.$search.val(""),r.tryUpdateList("")):y.blur(),e.preventDefault());else if("ArrowDown"===t||40===t){var s,l=r.$activeOption;if(l&&(s=l.next(".picker-option"),h))for(;s.length&&s.hasClass("picker-option-selected");)s=s.next(".picker-option");s&&s.length||(s=r.$optionsList.children(h?".picker-option:not(.picker-option-selected)":".picker-option").first()),s.length&&r.activeOption(s),e.preventDefault()}else if("ArrowUp"===t||30===t){var a,l=r.$activeOption;if(l&&(a=l.prev(".picker-option"),h))for(;a.length&&a.hasClass("picker-option-selected");)a=a.prev(".picker-option");a&&a.length||(a=r.$optionsList.children(h?".picker-option:not(.picker-option-selected)":".picker-option").last()),a.length&&r.activeOption(a),e.preventDefault()}else i.deleteByBackspace&&h&&("Backspace"===t||8===t)&&r.value&&r.value.length&&!y.val().length&&r.deselect(r.value[r.value.length-1])}}),h){g.on("mousedown",function(e){if(r.dropListShowed)return e.preventDefault(),void e.stopPropagation()}).on("mouseup",function(t){g.hasClass("sortable-sorting")||e(t.target).closest(".picker-selection-remove").length||r.dropListShowed||r.focus()});var w=i.sortValuesByDnd;if(w&&e.fn.sortable){f.addClass("picker-sortable");var C={selector:".picker-selection",stopPropagation:!0,start:function(){r.hideDropList()},finish:function(t){var i=[];e.each(t.list,function(e,t){i.push(t.item.data("value"))}),r.setValue(i.slice(),!1,!0)}};"object"==typeof w&&e.extend(C,w),g.sortable(C)}}g.on("click",".picker-selection-remove",function(t){if(r.multi){var i=e(this).closest(".picker-selection");r.deselect(i.data("value"))}else r.deselect();t.stopPropagation()}),p.on("chosen:updated",function(){r.updateFromSelect(),r.setValue(p.val(),!0),r.updateList()}).on("chosen:activate",r.focus).on("chosen:open",r.showDropList).on("chosen:close",r.hideDropList),f.addClass("picker-ready"),setTimeout(function(){r.triggerEvent("ready",{picker:r},"","chosen:ready")},0)};l.prototype.destroy=function(){var t=this,i=t.options;t.hideDropList();var r=t.$search;r.off("focus blur input change"),i.hotkey&&r.off("keydown"),r.remove();var n=t.$selections;n.off("click"),t.multi&&n.off("mousedown mouseup"),n.remove();var s=t.$formItem;t.selectOptionsBackup&&(s.empty(),e.each(t.selectOptionsBackup,function(t,o){var r={value:o[i.valueKey]},n=o[i.keysKey];void 0!==n&&(r["data-"+i.keysKey]=n),s.append(e("<option />").attr(r).text(o[i.textKey]))})),s.off("chosen:updated chosen:activate chosen:open chosen:close").val(t.value).show(),!t.selfFormItem&&t.$.hasClass("picker")||t.$container.remove(),this.destroyDropList(0),t.$.data(o,null)},l.prototype.focus=function(){this.$search.focus()},l.prototype.select=function(e,t){var i=this;if(!i.isSelectedValue(e)){if(i.triggerEvent("select",{value:e,picker:i})===!1)return;if(i.multi){var o=i.value;o=o?o.slice():[],o.push(e),i.setValue(o)}else i.setValue(e)}t||i.hideDropList()},l.prototype.deselect=function(e){var t=this;if(t.multi){if(!t.isSelectedValue(e))return;if(t.triggerEvent("deselect",{value:e,picker:t})===!1)return;var i=t.value;if(i&&i.length)for(var o=0;o<i.length;++o)if(i[o]===e){i=i.slice(),i.splice(o,1),t.setValue(i);break}}else t.setValue("")},l.prototype.updateMessage=function(e,t,i){var o=this,r=o.$message,n="string"==typeof e&&e.length;e=n?e:"",o.hasMessage=n,r.attr("title",e).text(e).attr("data-type",t),o.$dropMenu.toggleClass("picker-has-message",!!n),i||"top"!==o.dropDirection||o.layoutDropList()},l.prototype.getRemoteList=function(t,i){var o=this,r=o.options.remote;if(r){var n=o.options,s=o.search;if("string"==typeof s&&!s.length&&n.disableEmptySearch)return o.setList([]),void t(!1);var l,a={search:s,limit:n.maxListCount};if(l="string"==typeof r?{url:r}:"function"==typeof r?r(a,o):r,!l.url)return void console.warn("Remote url must provide to get remote list in picker.");var p=!1;l.url.indexOf("{search}")>-1&&(l.url=l.url.replace(/\{search\}/g,s),p=!0),l.url.indexOf("{limit}")>-1&&(l.url=l.url.replace(/\{limit\}/g,n.maxListCount),p=!0),o.updateMessage(""),o.$container.addClass("picker-loading"),o.remoteXhr&&o.remoteXhr.abort(),o.remoteXhr=e.ajax(e.extend({dataType:"json",dataFilter:n.remoteConverter,data:p?null:a},l)).done(function(i,r,s){var l=!1;if(i){if(e.isPlainObject(i))if("success"!==i.result&&"ok"!==i.result||!Array.isArray(i.data))if("fail"===i.result)o.updateMessage(i.message||JSON.stringify(i),"danger");else{var a=[];e.each(i,function(t,i){var o={};o[n.valueKey]=t,"object"==typeof i?e.extend(o,i):o[n.textKey]=i,a.push(o)}),i=a}else i=i.data;Array.isArray(i)&&(l=i.length,o.setList(i,n.remoteOnly))}t&&t(l)}).fail(function(e,t){var r,s=n.onRemoteError;r="function"==typeof s?s(e,t):"string"==typeof s?s:(n.remoteErrorHint||o.lang.remoteErrorHint).format(t||""),o.updateMessage(r,"danger"),i&&i()}).always(function(){o.remoteXhr=null,o.$container.removeClass("picker-loading")})}},l.prototype.layoutDropList=function(i,o,r){var n=this;if(n.$dropMenu){var s=n.options,l=s.maxDropHeight||Number.MAX_VALUE,a=n.$dropMenu,p=n.$optionsList;i||a.css({opacity:0,width:"auto","max-width":"none"}),p.css({"max-height":l}),setTimeout(function(){var i=n.$selections[0].getBoundingClientRect(),c=o?s.dropDirection:n.dropDirection||s.dropDirection;"function"==typeof c&&(c=c(i,n));var u=s.maxAutoDropWidth,d=e(t),h=d.height(),v=n.hasMessage?n.$message.outerHeight():0,f=Math.max(v,a.height()),g=s.dropWidth||"auto",m={left:i.left,opacity:1};"auto"===c&&(c=i.top+i.height+f>h&&i.top>h-i.top-i.height?"top":"bottom"),n.dropDirection=c;var y=Math.min(l,"bottom"===c?h-i.top-i.height:i.top);if(f=Math.min(f,y),m.top="bottom"===c?i.top+i.height:i.top-f,"100%"===g)m.width=i.width;else if("auto"===g){if(m.width="auto",m.maxWidth="auto"===u?i.width:u,n.multi){var k=n.$search[0].getBoundingClientRect();m.left=k.left}}else m.width=g;f>v&&p.css("max-height",f-v),a.css(m),r&&r()},0)}},l.prototype.tryUpdateList=function(e){var t=this;t.search!==e&&(t.search=e,t.updateListTimer&&clearTimeout(t.updateListTimer),t.updateListTimer=setTimeout(function(){t.updateListTimer=null,t.updateList()},t.options.searchDelay))},l.prototype.renderOptionsList=function(t,o){var r=this,n=r.$optionsList;if(void 0===t?t=r.optionsList:r.optionsList=t,n){var s="",l=r.options,a=r.search,p="string"==typeof a&&a.length,c=0;if(t.length){for(var u,d,h,v=l.maxListCount,f=l.valueKey,g=l.textKey,m=l.showMultiSelectedOptions,y=v?Math.min(t.length,v):t.length,k=a.toLowerCase(),L=n.children(".picker-option").addClass("picker-expired"),S=r.activeValue,x=void 0!==S&&null!==S,w=0;w<y;++w){var C=t[w],$=C[f],b=r.isSelectedValue($);if(m||!b||!r.multi){var D=C[g];if($.length&&(!l.hideEmptyTextOption||D.length)){c++;var M=r.getItemID(C,"option"),_=e(i.getElementById(M));_.length?_.removeClass("picker-expired"):_=e('<a class="picker-option" id="'+M+'" data-value="'+$+'"><span class="picker-option-text"></span><span class="picker-option-keys"></span></a>'),_.attr("title",D).removeClass(".picker-option-active").toggleClass("picker-option-selected",b);var O=_.find(".picker-option-text");if(p){var V=D.toLowerCase(),I=V.split(k);if(I.length>1){O.empty();var E=0,T=I[0].length;T&&(O.append(e("<span>").text(D.substr(E,T))),E+=T);for(var K=1;K<I.length;++K)O.append(e('<span class="picker-option-text-matched">').text(D.substr(E,a.length))),E+=a.length,T=I[K].length,T&&(O.append(e("<span>").text(D.substr(E,T))),E+=T)}else O.text(D)}else O.text(D);_.appendTo(n),r.multi?b||d||(d=C):!h&&x&&$===S?h=C:b?u=C:d||(d=C)}}}L.filter(".picker-expired").remove(),!o&&y<t.length&&(s=l.accurateSearchHint||r.lang.accurateSearchHint),r.activeOption(h||u||d)}else n.empty();c||o||(s=p?(l.emptySearchResultHint||r.lang.emptySearchResultHint).format(a):l.emptyResultHint||r.lang.emptyResultHint,p&&r.triggerEvent("noResults",{search:a,picker:r},"","chosen:no_results")),o||r.updateMessage(s,"info"),r.$dropMenu.toggleClass("picker-no-options",!c),r.layoutDropList(r.listRendered),r.listRendered=!0}},l.prototype.activeOption=function(t,i){var o=this;t&&(t instanceof e?t=t.attr("data-value"):"object"==typeof t&&(t=t[o.options.valueKey]));var r=o.getListItem(t);r?o.activeValue=t:t=o.activeValue,o.$optionsList.find(".picker-option-active").removeClass("picker-option-active");var n=o.$optionsList.find('[data-value="'+t+'"]');if(n.length){if(n.addClass("picker-option-active"),!i){var s=n[0];s.scrollIntoViewIfNeeded?s.scrollIntoViewIfNeeded():s.scrollIntoView&&s.scrollIntoView()}o.$activeOption=n}else o.$activeOption=null},l.prototype.updateList=function(e,t,i){var o=this;void 0!==e?o.search=e:e=o.search;var r=o.options.remoteOnly;if(r)o.layoutDropList(!1,!0);else{var n=[];if(null===e||void 0===e||"string"==typeof e&&!e.length)n=o.list||[];else if("function"==typeof o.options.list)n=o.options.list({search:e,limit:o.options.maxListCount});else if(o.list&&o.list.length){var s=o.options.maxListCount,l=o.options.keysKey,a=o.options.textKey,p=o.options.valueKey,c=o.options.searchValueKey,u={};e=e.toLowerCase();for(var d=0;d<o.list.length;++d){var h=o.list[d],v=h[p];if(!o.multi||!o.isSelectedValue(v)){var f=0,g=h[a];if(null!==g&&void 0!==g&&""!==g){g=g.toLowerCase();var m=g.indexOf(e);m>-1&&(f+=0===m?20:10)}if(!f){var y=h[l];if(null!==y&&void 0!==y&&""!==y){y=y.toLowerCase();var m=y.indexOf(e);m>-1&&(f+=0===m?8:4)}}if(!f&&c&&null!==v&&void 0!==v&&""!==v){v=v.toLowerCase();var m=v.indexOf(e);m>-1&&(f+=0===m?3:1)}if(f&&(u[v]=f+(o.list.length-d)/o.list.length,n.push(h)),s&&n.length>=s)break}}n.length&&(n=n.sort(function(e,t){return u[t[p]]-u[e[p]]}))}o.renderOptionsList(n,!1,i)}t||o.getRemoteList(function(t){r?o.renderOptionsList(o.list,!1,i):o.updateList(e,!0)},r?function(){o.renderOptionsList([],!0,i)}:null)},l.prototype.destroyDropList=function(e){var t=this;t._clearTimer&&clearTimeout(t._clearTimer),t.$dropMenu&&(e?t._clearTimer=setTimeout(t.destroyDropList.bind(t,0),e):(t.$optionsList.off("click mouseenter"),t.$optionsList=null,t.$dropMenu.remove(),t.$dropMenu=null,t.$message=null))},l.prototype.showDropList=function(){var t=this;if(t.triggerEvent("showingDrop",{picker:t})!==!1){if(t._clearTimer&&clearTimeout(t._clearTimer),t.dropListShowed=!0,t.dropDirection=null,t.listRendered=!1,t.activeValue=null,r[t.id]=t,t.options.disableScrollOnShow&&e.zui.fixBodyScrollbar(),!t.$dropMenu){var i=e('<div class="picker-drop-menu" id="pickerDropMenu-'+t.id+'"></div>').attr("data-id",t.id),n=e('<div class="picker-option-list"></div>').appendTo(i);i.data(o,t).toggleClass("picker-multi",t.multi).toggleClass("picker-single",!t.multi).appendTo("body"),t.options.chosenMode&&i.addClass("chosen-up"),n.on("click",".picker-option",function(){t.select(e(this).attr("data-value"))}).on("mouseenter",".picker-option",function(){t.activeOption(e(this),!0)});var s=e('<div class="picker-message"></div>').appendTo(i);t.$dropMenu=i,t.$message=s,t.$optionsList=n}t.updateList(t.search,!1,function(){t.triggerEvent("showedDrop",{picker:t},"","chosen:showing_dropdown")}),t.$dropMenu.addClass("picker-drop-show")}},l.prototype.hideDropList=function(){var t=this;if(t.triggerEvent("hidingDrop",{picker:t})!==!1){t.dropListShowed=!1,t.$activeOption=null,t.activeValue=null,t.$search.val(""),t.search="",delete r[t.id],t.$dropMenu&&t.$dropMenu.removeClass("picker-drop-show"),t.options.disableScrollOnShow&&e.zui.resetBodyScrollbar(),t.triggerEvent("hiddenDrop",{picker:t},"","chosen:hiding_dropdown");var i=t.options.autoClearDrop;i&&t.destroyDropList(i)}},l.prototype.updateFromSelect=function(t){var i=this,o=i.options,r=[];void 0===t&&(t=!0),i.$formItem.children("option").each(function(){var t=e(this),i=t.text(),n=t.val();if(i.length||n.length){var s={};s[o.valueKey]=n,s[o.textKey]=i,s[o.keysKey]=t.data(o.keysKey),r.push(s)}var l=o.allowSingleDeselect;"auto"!==l&&null!==l&&void 0!==l||n.length||(o.allowSingleDeselect=!0)}),i.selectOptionsBackup=r.slice(),i.setList(r,t)},l.prototype.setList=function(e,t){var i=this,o=i.options,r=t?[]:i.list||[],n=t?{}:i.listMap||{};"string"==typeof e&&(e=e.split(o.multiValueSplitter));for(var s=0;s<e.length;++s){var l=e[s];if("string"==typeof l){var a={};a[o.textKey]=l,a[o.valueKey]=String(s),l=a}else if(Array.isArray(l)){var a={};a[o.textKey]=l[0],a[o.valueKey]=l[1],a[o.keysKey]=l[2],l=a}var p=l[o.valueKey];"string"!=typeof p&&(p=String(p),l[o.valueKey]=p);var c=n[p];c?(l.index=c.$_index,r[c.$_index]=l,n[p]=l):(l.$_index=r.length,n[p]=l,r.push(l))}i.list=r,i.listMap=n},l.prototype.removeFromList=function(e){var t=this,i=t.list||[];if(i.length){var o=t.options;"string"==typeof e&&(e=e.split(o.multiValueSplitter));for(var r={},n=0;n<e.length;++n){var s=e[n];r["object"==typeof s?s[o.valueKey]:s]=!0}for(var l=[],a={},n=0;n<i.length;++n){var s=i[n],p=s[o.valueKey];r[p]||(l.push(s),a[p]=s)}t.list=l,t.listMap=a,t.setValue(t.multi?t.value.slice():t.value)}},l.prototype.getItemID=function(e,t){var i=this.id+"-item-"+encodeURIComponent(e[this.options.valueKey]);return void 0!==t?i+"-"+t:i},l.prototype.isSelectedValue=function(t){var i=this;if(void 0===i.value||null===i.value)return!1;if("string"!=typeof t&&(t=String(t)),i.multi){if(!i.valueSet)if(void 0!==typeof Set)i.valueSet=new Set(i.value);else{i.valueSet={};for(var o=0;o<i.value.length;++o)i.valueSet[i.value[o]]=!0}return e.isPlainObject(i.valueSet)?!!i.valueSet[t]:i.valueSet.has(t)}return t===i.value},l.prototype.getValue=function(){return that.value},l.prototype.getListItem=function(e){return this.listMap[e]},l.prototype.hasListItem=function(e){return void 0!==this.listMap[e]},l.prototype.triggerEvent=function(e,t,i,o){var r=this;if(Array.isArray(t)||(t=[t]),r.$.trigger(e,t),r.options.chosenMode&&o&&r.$.trigger(o,t),i=i===!0?e:i||"on"+e[0].toUpperCase()+e.substr(1),"function"==typeof r.options[i])return r.options[i].apply(r,t)},l.prototype.setValue=function(t,o,r){var n,s=this,l=s.options,a=s.multi;if(void 0===t&&(t=s.$formItem.val()),null===t&&(t=""),a&&"string"==typeof t?t=t.split(l.multiValueSplitter):a||"string"==typeof t||(t=String(t)),l.valueMustInList&&!l.remoteOnly)if(a&&t){for(var p=[],c=0;c<t.length;++c){var u=t[c];s.hasListItem(u)&&p.push(u)}p.length!==t.length&&(t=p)}else a||s.hasListItem(t)||(t=void 0!==l.defaultValue?l.defaultValue:"");if(a&&s.value&&t?s.value.join(l.multiValueSplitter)!==t.join(l.multiValueSplitter):s.value!==t){if(!o&&s.triggerEvent("beforeChange",{value:t,oldValue:s.value},!0)===!1)return;s.value=t,n=!0}a&&(s.valueSet=null);var d=s.$formItem;if("select"===s.formType){var h=l.chosenMode;h||d.empty(),a?e.each(t,function(e,t){h&&d.find('option[value="'+t+'"]').length||d.append('<option value="'+t+'">')}):h&&d.find('option[value="'+t+'"]').length||d.append('<option value="'+t+'">')}if(d.val(t),!r){var v=!1;if(a){var f=s.$selections,g=f.children(".picker-selection").addClass("picker-expired");e.each(t,function(t,o){void 0===o||null===o?o="":"string"!=typeof o&&(o=String(o));var r=s.getListItem(o);if(r){v=!0;var n=r[l.textKey],a=s.getItemID(r,"selection"),p=e(i.getElementById(a));p.length?p.removeClass("picker-expired"):p=e('<div class="picker-selection" id="'+a+'"><span class="picker-selection-text"></span><span class="picker-selection-remove"></span></div>').data("value",o),p.find(".picker-selection-text").text(n),p.attr("title",n).insertBefore(s.$search)}}),g.filter(".picker-expired").remove()}else{var m=s.getListItem(t);v=!!m,s.$singleSelection.find(".picker-selection-text").text(v?m[l.textKey]:"")}s.$container.toggleClass("picker-no-value",!v).toggleClass("picker-has-value",v)}s.dropListShowed&&s.renderOptionsList(),n&&(l.onChange&&l.onChange(t),o||s.triggerEvent("change",{value:t,picker:s}),s.$[0]!==d[0]&&d.change())},l.prototype.handleWindowScroll=function(){var e=this;e.options.disableScrollOnShow||(e.options.hideOnWindowScroll?(e.scrollEventTimer&&clearTimeout(e.scrollEventTimer),e.$dropMenu.css("opacity",0),e.scrollEventTimer=setTimeout(function(){e.scrollEventTimer=null,e.dropListShowed&&e.layoutDropList(!0,!0)},500)):e.layoutDropList(!0,!0))},l.DEFAULTS=n,l.NAME=o,l.SHOWS=r,l.convertChosenOptions=function(t){var i=!1;return e.each({allow_single_deselect:"allowSingleDeselect",inherit_select_classes:"inheritFormItemClasses",max_selected_options:"maxSelectedCount",no_results_text:"emptySearchResultHint",placeholder_text:"placeholder",placeholder_text_multiple:"placeholder",placeholder_text_single:"placeholder",single_backstroke_delete:"deleteByBackspace",display_selected_options:"showMultiSelectedOptions",drop_direction:"dropDirection",drop_width:"dropWidth",max_drop_width:"maxAutoDropWidth",highlight_selected:"autoSelectFirst",sort_value_splitter:"multiValueSplitter"},function(e,o){var r=t[e];void 0!==r&&(t[o]=r,delete t[e],i=!0)}),i&&(t.chosenMode=!0),t},e.fn.picker=function(t,i){return this.each(function(){var r=e(this),n=r.data(o),s="object"==typeof t&&t;n?"string"==typeof t&&n[t](i):((l.enabledChosenMode||s&&s.chosenMode)&&(s=l.convertChosenOptions(e.extend({},r.data(),s))),r.data(o,n=new l(this,s)))})},e.fn.picker.Constructor=l,e.Picker=e.zui.Picker=l,l.enableChosen=function(){l.enabledChosenMode||(e.fn.chosen=function(t,i){return this.each(function(){var r=e(this),n=r.data(o);if(!n){var s=e.extend({},r.data(),"object"==typeof t?t:null);return n=new l(this,l.convertChosenOptions(s)),void r.data(o,n)}"string"==typeof t&&n[t](i)})},e.fn._chosen=e.fn.chosen,l.enabledChosenMode=!0)},e.zui.fixBodyScrollbar&&e.zui({_scrollbarWidth:0,checkBodyScrollbar:function(){if(i.body.clientWidth>=t.innerWidth)return 0;if(!e.zui._scrollbarWidth){var o=i.createElement("div");o.className="modal-scrollbar-measure scrollbar-measure",i.body.appendChild(o),e.zui._scrollbarWidth=o.offsetWidth-o.clientWidth,i.body.removeChild(o)}return e.zui._scrollbarWidth},fixBodyScrollbar:function(){if(e.zui.checkBodyScrollbar()){var t=e("body"),i=parseInt(t.css("padding-right")||0,10);return e.zui._scrollbarWidth&&t.css({paddingRight:i+e.zui._scrollbarWidth,overflowY:"hidden"}),!0}},resetBodyScrollbar:function(){e("body").css({paddingRight:"",overflowY:""})}}),e(function(){e('[data-toggle="picker"]').picker(),e(i).on("mousedown",function(t){var i=e(t.target).closest(".picker,.picker-drop-menu"),o=i.length;e.each(r,function(e,t){o&&(i.is(t.$container)||t.$dropMenu&&i.is(t.$dropMenu))||t.hideDropList()})}),e(t).on("scroll",function(t){e.each(r,function(e,i){i.handleWindowScroll(t)})})})}(jQuery,window,document);