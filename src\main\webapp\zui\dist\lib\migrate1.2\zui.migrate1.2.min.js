/*!
 * ZUI: 1.2升级到1.3兼容插件 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(e,r){function o(r,s){if(Array.isArray(r))return void e.each(r,function(e,r){o(r,s)});var i={};i[r]=a[r],s?e.extend(s,i):e.extend(i)}var a=e.zui;a&&(o(["uuid","callEvent","clientLang","browser","messager","Messager","showMessager","closeModal","ajustModalPosition","ModalTrigger","modalTrigger","store"]),o(["Color","imgReady","messager","Messager","showMessager","closeModal","ajustModalPosition","ModalTrigger","modalTrigger","store"],r))}(jQuery,window);