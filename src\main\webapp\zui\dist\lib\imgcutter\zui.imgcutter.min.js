/*!
 * ZUI: 图片裁剪工具 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t,i,h){"use strict";t.fn.draggable||console.error("img-cutter requires draggable.js"),t.zui.imgReady||console.error("img-cutter requires image.ready.js");var e="zui.imgCutter",o=function(i,h){this.$=t(i),this.initOptions(h),this.init()};o.DEFAULTS={coverColor:"#000",coverOpacity:.6,defaultWidth:128,defaultHeight:128,minWidth:48,minHeight:48},o.prototype.callEvent=function(t,i){var o=this.$.callEvent(t+"."+e,i,this);return!(o.result!==h&&!o.result)},o.prototype.initOptions=function(i){this.options=t.extend({},o.DEFAULTS,this.$.data(),i),this.options.coverOpacityIE=100*this.options.coverOpacity,this.clipWidth=this.options.defaultWidth,this.clipHeight=this.options.defaultHeight},o.prototype.init=function(){this.initDom(),this.initSize(),this.bindEvents()},o.prototype.initDom=function(){this.$canvas=this.$.children(".canvas"),this.$img=this.$canvas.children("img"),this.$actions=this.$.children(".actions"),this.$btn=this.$.find(".img-cutter-submit"),this.$preview=this.$.find(".img-cutter-preview"),this.options.img=this.$img.attr("src"),this.$canvas.append('<div class="cover" style="background: {coverColor}; opacity: {coverOpacity}; filter:alpha(opacity={coverOpacityIE});"></div><div class="controller" style="width: {defaultWidth}px; height: {defaultHeight}px"><div class="control" data-direction="top"></div><div class="control" data-direction="right"></div><div class="control" data-direction="bottom"></div><div class="control" data-direction="left"></div><div class="control" data-direction="top-left"></div><div class="control" data-direction="top-right"></div><div class="control" data-direction="bottom-left"></div><div class="control" data-direction="bottom-right"></div></div><div class="cliper"><img src="{img}"/></div>'.format(this.options)),this.$cover=this.$canvas.children(".cover"),this.$controller=this.$canvas.children(".controller"),this.$cliper=this.$canvas.children(".cliper"),this.$chipImg=this.$cliper.children("img"),this.options.fixedRatio&&this.$.addClass("fixed-ratio")},o.prototype.resetImage=function(t){var i=this;i.options.img=t,i.$img.attr("src",t),i.$chipImg.attr("src",t),i.imgWidth=h,i.left=h,i.initSize()},o.prototype.initSize=function(){var e=this;e.imgWidth||t.zui.imgReady(e.options.img,function(){e.imgWidth=this.width,e.imgHeight=this.height,(this.width<e.options.minWidth||this.height<e.options.minHeight)&&(e.options.onSizeError&&e.options.onSizeError({width:this.width,height:this.height}),e.options.minWidth=i.min(this.width,e.options.minWidth),e.options.minHeight=i.min(this.height,e.options.minHeight)),e.callEvent("ready")});var o=setInterval(function(){e.imgWidth&&(clearInterval(o),e.width=i.min(e.imgWidth,e.$.width()),e.$canvas.css("width",this.width),e.$cliper.css("width",this.width),e.height=e.$canvas.height(),e.left===h&&(e.left=i.floor((e.width-e.$controller.width())/2),e.top=i.floor((e.height-e.$controller.height())/2)),e.refreshSize())},0)},o.prototype.refreshSize=function(t){var h=this.options;this.clipWidth=i.max(h.minWidth,i.min(this.width,this.clipWidth)),this.clipHeight=i.max(h.minHeight,i.min(this.height,this.clipHeight)),h.fixedRatio&&(t&&"height"===t?(this.clipWidth=i.max(h.minWidth,i.min(this.width,this.clipHeight*h.defaultWidth/h.defaultHeight)),this.clipHeight=this.clipWidth*h.defaultHeight/h.defaultWidth):(this.clipHeight=i.max(h.minHeight,i.min(this.height,this.clipWidth*h.defaultHeight/h.defaultWidth)),this.clipWidth=this.clipHeight*h.defaultWidth/h.defaultHeight)),this.left=i.min(this.width-this.clipWidth,i.max(0,this.left)),this.top=i.min(this.height-this.clipHeight,i.max(0,this.top)),this.right=this.left+this.clipWidth,this.bottom=this.top+this.clipHeight,this.$controller.css({left:this.left,top:this.top,width:this.clipWidth,height:this.clipHeight}),this.$cliper.css("clip","rect({0}px {1}px {2}px {3}px".format(this.top,this.left+this.clipWidth,this.top+this.clipHeight,this.left)),this.callEvent("change",{top:this.top,left:this.left,bottom:this.bottom,right:this.right,width:this.clipWidth,height:this.clipHeight})},o.prototype.getData=function(){var t=this;return t.data={originWidth:t.imgWidth,originHeight:t.imgHeight,scaleWidth:t.width,scaleHeight:t.height,width:t.right-t.left,height:t.bottom-t.top,left:t.left,top:t.top,right:t.right,bottom:t.bottom,scaled:t.imgWidth!=t.width||t.imgHeight!=t.height},t.data},o.prototype.bindEvents=function(){var h=this,e=this.options;this.$.resize(this.initSize.bind(this)),this.$btn.hover(function(){h.$.toggleClass("hover")}).click(function(){var i=h.getData();if(h.callEvent("before",i)){var o=e.post||e.get||e.url||null;null!==o&&t.ajax({type:e.post?"POST":"GET",url:o,data:i}).done(function(t){h.callEvent("done",t)}).fail(function(t){h.callEvent("fail",t)}).always(function(t){h.callEvent("always",t)})}}),this.$controller.draggable({move:!1,container:this.$canvas,drag:function(t){h.left+=t.smallOffset.x,h.top+=t.smallOffset.y,h.refreshSize()}}),this.$controller.children(".control").draggable({move:!1,container:this.$canvas,stopPropagation:!0,drag:function(t){var o=t.element.data("direction"),s=t.smallOffset,n=!1;switch(o){case"left":case"top-left":case"bottom-left":h.left+=s.x,h.left=i.min(h.right-e.minWidth,i.max(0,h.left)),h.clipWidth=h.right-h.left;break;case"right":case"top-right":case"bottom-right":h.clipWidth+=s.x,h.clipWidth=i.min(h.width-h.left,i.max(e.minWidth,h.clipWidth))}switch(o){case"top":case"top-left":case"top-right":h.top+=s.y,h.top=i.min(h.bottom-e.minHeight,i.max(0,h.top)),h.clipHeight=h.bottom-h.top,n=!0;break;case"bottom":case"bottom-left":case"bottom-right":h.clipHeight+=s.y,h.clipHeight=i.min(h.height-h.top,i.max(e.minHeight,h.clipHeight)),n=!0}h.refreshSize(n)}})},t.fn.imgCutter=function(i){return this.each(function(){var h=t(this),s=h.data(e),n="object"==typeof i&&i;s||h.data(e,s=new o(this,n)),"string"==typeof i&&s[i]()})},t.fn.imgCutter.Constructor=o,t(function(){t('[data-toggle="imgCutter"]').imgCutter()})}(jQuery,Math,void 0);