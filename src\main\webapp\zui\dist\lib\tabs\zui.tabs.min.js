/*!
 * ZUI: 标签页管理器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(e){"use strict";var t=function(t){var a=this;"string"==typeof t?a.url=t:e.isPlainObject(t)&&e.extend(a,t),a.id||(a.id=e.zui.uuid()),a.type||(a.iframe?(a.type="iframe",a.url=a.url||a.iframe):a.ajax?(a.type="ajax",a.url=a.url||(e.isPlainObject(a.ajax)?a.ajax.url:a.ajax)):a.url?a.type=t.ajax?"ajax":"iframe":a.type="custom"),a.createTime=(new Date).getTime(),a.openTime=0,a.onCreate&&a.onCreate.call(a)};t.prototype.open=function(){var e=this;e.openTime=(new Date).getTime(),e.onOpen&&e.onOpen.call(e)},t.prototype.close=function(){var e=this;e.openTime=0,e.onClose&&e.onClose.call(e)},t.create=function(e){return e instanceof t?e:new t(e)};var a="zui.tabs",n={tabs:[],defaultTabIcon:"icon-window",contextMenu:!0,errorTemplate:'<div class="alert alert-block alert-danger with-icon"><i class="icon-warning-sign"></i><div class="content">{0}</div></div>',showMessage:!0,navTemplate:'<nav class="tabs-navbar"></nav>',containerTemplate:'<div class="tabs-container"></div>'},o={zh_cn:{reload:"重新加载",close:"关闭",closeOthers:"关闭其他标签页",closeRight:"关闭右侧标签页",reopenLast:"恢复上次关闭的标签页",errorCannotFetchFromRemote:"无法从远程服务器（{0}）获取内容。"},zh_tw:{reload:"重新加載",close:"關閉",closeOthers:"關閉其他標籤頁",closeRight:"關閉右側標籤頁",reopenLast:"恢復上次關閉的標籤頁",errorCannotFetchFromRemote:"無法從遠程服務器（{0}）獲取內容。"},en:{reload:"Reload",close:"Close",closeOthers:"Close others",closeRight:"Close right",reopenLast:"Reopen last",errorCannotFetchFromRemote:"Cannot fetch data from remote server {0}."}},i=function(i,s){var r=this;r.name=a,r.$=e(i),s=r.options=e.extend({},n,this.$.data(),s);var l=e.zui.clientLang(),c=s.lang;e.isPlainObject(c)?r.lang=e.zui.getLangData?e.zui.getLangData(a,l,o):e.extend(!0,{},o[c.lang||l],c):(c=c||l,r.lang=e.zui.getLangData?e.zui.getLangData(a,c,o):o[c]||o.en);var d=r.$.find(".tabs-navbar");d.length||(d=e(s.navTemplate).appendTo(r.$)),r.$navbar=d;var v=d.find(".tabs-nav");v.length||(v=e('<ul class="tabs-nav nav nav-tabs"></ul>').appendTo(d)),r.$nav=v;var p=r.$.find(".tabs-container");p.length||(p=e(s.containerTemplate).appendTo(r.$)),r.$tabs=p,r.activeTabId=s.defaultTab;var u=s.tabs||[];r.tabs={},e.each(u,function(e,a){var n=t.create(a);r.tabs[n.id]=n,r.activeTabId||(r.activeTabId=n.id),r.renderTab(n)}),r.closedTabs=[],r.open(r.getActiveTab()),v.on("click."+a,".tab-nav-link",function(){r.open(r.getTab(e(this).data("id")))}).on("click."+a,".tab-nav-close",function(t){r.close(e(this).closest(".tab-nav-link").data("id")),t.stopPropagation()}).on("resize."+a,function(){r.adjustNavs()}),s.contextMenu&&v.contextmenu({selector:".tab-nav-link",itemsCreator:function(t){return r.createMenuItems(r.getTab(e(this).data("id")))},onShow:function(){r.$.addClass("tabs-show-contextmenu")},onHide:function(){r.$.removeClass("tabs-show-contextmenu")}})};i.prototype.createMenuItems=function(t){var a=this,n=a.lang;return[{label:n.reload,onClick:function(){a.open(t,!0)}},"-",{label:n.close,disabled:t.forbidClose,onClick:function(){a.close(t.id)}},{label:n.closeOthers,disabled:a.$nav.find(".tab-nav-item:not(.hidden)").length<=1,onClick:function(){a.closeOthers(t.id)}},{label:n.closeRight,disabled:!e("#tab-nav-item-"+t.id).next(".tab-nav-item:not(.hidden)").length,onClick:function(){a.closeRight(t.id)}},"-",{label:n.reopenLast,disabled:!a.closedTabs.length,onClick:function(){a.reopen()}}]},i.prototype.adjustNavs=function(e){var t=this;if(!e)return t.adjustNavsTimer&&clearTimeout(t.adjustNavsTimer),void(t.adjustNavsTimer=setTimeout(function(){t.adjustNavs(!0)},50));t.adjustNavsTimer&&(t.adjustNavsTimer=null);var a=t.$nav,n=a.find(".tab-nav-item:not(.hidden)"),o=a.width(),i=n.length,s=Math.floor(o/i);s<96&&(s=Math.floor((o-96)/(i-1))),a.toggleClass("tab-nav-condensed",s<=50),n.css("max-width",s)},i.prototype.renderTab=function(t,a){var n=this,o=(n.$nav,e("#tab-nav-item-"+t.id));if(!o.length){var i=e('<a class="tab-nav-link"><i class="icon"></i><span class="title"></span><i class="close tab-nav-close" title="'+n.lang.close+'">&times;</i></a>').attr({href:"#tabs-item-"+t.id,"data-id":t.id});if(o=e('<li class="tab-nav-item" data-id="'+t.id+'" id="tab-nav-item-'+t.id+'" />').append(i).appendTo(n.$nav),a){var s=e("#tab-nav-item-"+a);s.length&&o.insertAfter(s)}n.adjustNavs()}var i=o.find("a").attr("title",t.desc).toggleClass("not-closable",!!t.forbidClose);return i.find(".icon").attr("class","icon "+(t.icon||n.options.defaultTabIcon)),i.find(".title").text(t.title||t.defaultTitle||""),o},i.prototype.getActiveTab=function(){var e=this;return e.activeTabId?e.tabs[e.activeTabId]:null},i.prototype.getTab=function(e){var t=this;return e?("object"==typeof e&&(e=e.id),t.tabs[e]):t.getActiveTab()},i.prototype.close=function(t,a){var n=this,o=n.getTab(t);if(o&&(a||!o.forbidClose)){e("#tab-nav-item-"+o.id).remove(),e("#tab-"+o.id).remove(),o.close(),delete n.tabs[o.id],n.closedTabs.push(o),n.$.callComEvent(n,"onClose",o);var i;e.each(n.tabs,function(e,t){(!i||i.openTime<t.openTime)&&(i=t)}),i&&n.open(i)}},i.prototype.open=function(a,n){var o=this;a instanceof t||(a=t.create(a));var i=o.renderTab(a);o.$nav.find(".tab-nav-item.active").removeClass("active"),i.addClass("active");var s=e("#tab-"+a.id);s.length||(s=e('<div class="tab-pane" id="tab-'+a.id+'" />').appendTo(o.$tabs)),o.$tabs.find(".tab-pane.active").removeClass("active"),s.addClass("active"),a.open(),o.activeTabId=a.id,o.tabs[a.id]=a,!n&&a.loaded||o.reload(a),o.$.callComEvent(o,"onOpen",a)},i.prototype.showMessage=function(t,a){e.zui.messager.show(t,e.extend({placement:"center"},this.options.messagerOptions,{type:a}))},i.prototype.reload=function(t){var a=this;if("string"==typeof t?t=a.getTab(t):t||(t=a.getActiveTab()),t){if(!t.openTime)return a.open(t);var n=e("#tab-nav-item-"+t.id).addClass("loading").removeClass("has-error"),o=e("#tab-"+t.id).addClass("loading").removeClass("has-error"),i=function(i,s){if(t.openTime){if(n.removeClass("loading"),o.removeClass("loading"),a.$.callComEvent(a,"onLoad",t),("string"==typeof i||i instanceof e)&&(t.contentConverter&&(i=t.contentConverter(i,t)),o.empty().append(i),t.title||(i=o.text().replace(/\n/g,""),t.title=i.length>10?i.substr(0,10):i,a.renderTab(t))),s){n.addClass("has-error"),o.addClass("has-error");var r=a.options.showMessage;r&&("function"==typeof r&&(s=r(s)),a.showMessage(s,"danger")),i||o.html(a.options.errorTemplate.format(s))}t.loaded=(new Date).getTime()}};if("ajax"===t.type){var s={type:"get",url:t.url,error:function(e,n,o){i(!1,a.lang.errorCannotFetchFromRemote.format(t.url))},success:function(e){i(e)}};e.isPlainObject(t.ajax)&&(s=e.extend(s,t.ajax)),e.ajax(s)}else if("iframe"===t.type)try{var r="tab-iframe-"+t.id,l=e('<iframe id="'+r+'" name="'+r+'" src="'+t.url+'" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: 100%; left: 0px;"></iframe>');l.appendTo(o.empty()),e('<div class="tab-iframe-cover" />').appendTo(o);var c=document.getElementById(r);c.onload=c.onreadystatechange=function(){if(!this.readyState||"complete"==this.readyState){i();var e=c.contentDocument;e&&!t.title&&(t.title=e.title,a.renderTab(t))}}}catch(d){i()}else{var v=t.content||t.custom;"function"==typeof v?(v=v(t,i,a),v!==!0&&i(v)):i(v)}}},i.prototype.closeOthers=function(t){var a=this;a.$nav.find(".tab-nav-link:not(.hidden)").each(function(){var n=e(this).data("id");n!==t&&a.close(n)})},i.prototype.closeRight=function(t){for(var a=e("#tab-nav-item-"+t),n=a.next(".tab-nav-item:not(.hidden)");n.length;)this.close(n.data("id")),n=a.next(".tab-nav-item:not(.hidden)")},i.prototype.closeAll=function(){var t=this;t.$nav.find(".tab-nav-link:not(.hidden)").each(function(){t.close(e(this).data("id"))})},i.prototype.reopen=function(){var e=this;e.closedTabs.length&&e.open(e.closedTabs.pop(),!0)},e.fn.tabs=function(t){return this.each(function(){var n=e(this),o=n.data(a),s="object"==typeof t&&t;o||n.data(a,o=new i(this,s)),"string"==typeof t&&o[t]()})},i.NAME=a,e.fn.tabs.Constructor=i}(jQuery);