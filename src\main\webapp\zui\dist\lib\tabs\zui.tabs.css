/*!
 * ZUI: 标签页管理器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */

.tabs {
  position: relative;
  min-height: 400px;
  }
.tabs-navbar {
  padding: 4px 4px 0 4px;
  }
.tabs-nav {
  height: 30px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border-bottom: none;
  border-bottom: 1px solid #ddd;
  }
.tab-nav-item {
  width: 160px;
  min-width: 0;
  max-width: 160px;
  padding-right: 2px;
  margin-bottom: 0;
  border: none;
  }
.tab-nav-item:hover {
  min-width: 95px;
  }
.tab-nav-link {
  position: relative;
  height: 30px;
  margin: 0;
  overflow: hidden;
  background-color: rgba(255, 255, 255, .65);
  background-color: #e5e5e5;
  border-color: #ddd;
  border-bottom: none;
  border-radius: 2px 2px 0 0;
  }
.tab-nav-link > .title {
  position: absolute;
  top: 5px;
  right: 5px;
  left: 30px;
  display: block;
  overflow: hidden;
  font-size: 14px;
  line-height: 20px;
  text-overflow: ellipsis;
  white-space: nowrap;
  }
.tab-nav-link > .icon {
  position: absolute;
  top: 5px;
  left: 5px;
  display: block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  opacity: .8;
  }
.tab-nav-item.loading .tab-nav-link > .icon:before {
  content: '\e97b';
  -webkit-animation: spin 2s infinite linear;
       -o-animation: spin 2s infinite linear;
          animation: spin 2s infinite linear;
  }
.tab-nav-link > .close {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  font-weight: 200;
  line-height: 16px;
  text-align: center;
  text-shadow: none;
  visibility: hidden;
  border-radius: 4px;
  opacity: 0;
  -webkit-transition: all .2s;
       -o-transition: all .2s;
          transition: all .2s;
  }
.tab-nav-link > .close:hover {
  color: #fff;
  background-color: #ea644a;
  }
.tab-nav-link:hover > .title {
  right: 25px;
  }
.tab-nav-link:hover > .close {
  visibility: visible;
  opacity: 1;
  }
.tab-nav-link.not-closable > .close {
  display: none;
  }
.nav-tabs.tabs-nav > li > a,
.nav-tabs.tabs-nav > li > a:hover,
.nav-tabs.tabs-nav > li > a:focus {
  border-color: #ddd #ddd transparent #ddd;
  }
.tab-nav-condensed .tab-nav-link > .title {
  left: 5px;
  text-overflow: initial;
  }
.tab-nav-condensed .tab-nav-link > .icon {
  display: none;
  }
.tabs-container {
  position: absolute;
  top: 34px;
  right: 0;
  bottom: 0;
  left: 0;
  }
.tabs-container > .tab-pane {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  }
.tabs-container > .tab-pane.active {
  display: block;
  }
.tab-iframe-cover {
  display: none;
  }
.tabs-show-contextmenu .tab-iframe-cover {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  }
