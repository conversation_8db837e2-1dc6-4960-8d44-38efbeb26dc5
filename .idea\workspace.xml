<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="servlet:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c9817ea5-8c3d-4ced-a2a5-4a42d8c425ac" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://D:/jdk/lib/src.zip!/java.base/java/lang/Class.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\reposotiry" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zr0kfdcNp60jXXv1MpajeRWYvc" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Tomcat 服务器.Tomcat 10.0.13.executor&quot;: &quot;Run&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/servlet&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.build.tools.auto.reload&quot;: &quot;ALL&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.augmentcode.intellij.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.JdbcUtil.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\servlet\src\main\java\com\ruoyi\servlet" />
      <recent name="C:\Users\<USER>\Desktop\servlet\src\main\webapp\zui" />
      <recent name="C:\Users\<USER>\Desktop\servlet\src\main\webapp" />
    </key>
  </component>
  <component name="RunManager" selected="Tomcat 服务器.Tomcat 10.0.13">
    <configuration name="JdbcUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.ruoyi.servlet.jdbc.JdbcUtil" />
      <module name="servlet" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ruoyi.servlet.jdbc.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat 10.0.13" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 10.0.13" ALTERNATIVE_JRE_ENABLED="false">
      <option name="BROWSER_ID" value="98ca6316-2f89-46d9-a9e5-fa9e2b0625b3" />
      <deployment>
        <artifact name="servlet:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="1e3fbf05-e550-498c-aad3-b9b138d02060" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="56851" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="servlet:war exploded" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="Tomcat 服务器.Tomcat 10.0.13" />
      <item itemvalue="应用程序.JdbcUtil" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.JdbcUtil" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c9817ea5-8c3d-4ced-a2a5-4a42d8c425ac" name="更改" comment="" />
      <created>1752474029418</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752474029418</updated>
      <workItem from="1752474030691" duration="3417000" />
      <workItem from="1752482731486" duration="823000" />
      <workItem from="1752489213779" duration="200000" />
      <workItem from="1752541049582" duration="26469000" />
      <workItem from="1752575346338" duration="4476000" />
      <workItem from="1752627628815" duration="19878000" />
      <workItem from="1752658301155" duration="3861000" />
      <workItem from="1752714140379" duration="17758000" />
      <workItem from="1752800683432" duration="18081000" />
      <workItem from="1752837367414" duration="363000" />
      <workItem from="1752837750411" duration="197000" />
      <workItem from="1752845527976" duration="382000" />
      <workItem from="1752845926459" duration="63000" />
      <workItem from="1752846108662" duration="2519000" />
      <workItem from="1752848954769" duration="4240000" />
      <workItem from="1752913210038" duration="4232000" />
      <workItem from="1752925375475" duration="69000" />
      <workItem from="1752925457786" duration="261000" />
      <workItem from="1752925728276" duration="1100000" />
      <workItem from="1752926861646" duration="738000" />
      <workItem from="1752927619583" duration="1395000" />
      <workItem from="1752929997663" duration="147000" />
      <workItem from="1752930149785" duration="790000" />
      <workItem from="1752933383152" duration="5584000" />
      <workItem from="1752939676700" duration="194000" />
      <workItem from="1752939887554" duration="147000" />
      <workItem from="1752940041929" duration="10000" />
      <workItem from="1752940064010" duration="112000" />
      <workItem from="1752940182302" duration="10000" />
      <workItem from="1752940213107" duration="155000" />
      <workItem from="1752940387751" duration="610000" />
      <workItem from="1752942259078" duration="232000" />
      <workItem from="1752942503979" duration="1487000" />
      <workItem from="1752980042029" duration="2789000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/ruoyi/dao/impl/ParcelImpl.java</url>
          <line>58</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory">
        <watch expression="&lt;select&gt;&#10;                            &lt;option&gt;易碎物品&lt;/option&gt;&#10;                            &lt;option&gt;贵重物品&lt;/option&gt;&#10;                            &lt;option&gt;普通物品&lt;/option&gt;&#10;                        &lt;/select&gt;" />
      </configuration>
    </watches-manager>
  </component>
</project>