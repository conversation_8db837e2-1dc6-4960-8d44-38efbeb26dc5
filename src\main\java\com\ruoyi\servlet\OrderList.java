package com.ruoyi.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import com.ruoyi.dao.ParcelDao;
import com.ruoyi.dao.impl.ParcelImpl;
import com.ruoyi.entity.Parcel;
import com.ruoyi.utils.JsonUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@WebServlet("/parcel/list")
public class OrderList extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType("application/json;charset=UTF-8");
        Integer userId = (Integer) req.getSession().getAttribute("userId");
        ParcelDao dao = new ParcelImpl();
        List<Parcel> parcelList = null;
        try {
            parcelList = dao.getParcelList(userId);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        resp.getWriter().write(JsonUtil.toJson(parcelList));
    }
    
}
