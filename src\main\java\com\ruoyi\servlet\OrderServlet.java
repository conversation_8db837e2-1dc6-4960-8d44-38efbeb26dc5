package com.ruoyi.servlet;

import com.ruoyi.dao.ParcelDao;
import com.ruoyi.dao.TrackDao;
import com.ruoyi.dao.impl.ParcelImpl;
import com.ruoyi.dao.impl.TrackDaoImpl;
import com.ruoyi.entity.Parcel;
import com.ruoyi.entity.Tracking;
import com.ruoyi.entity.User;
import com.ruoyi.utils.JsonUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

@WebServlet("/OrderServlet")
public class OrderServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String action = req.getParameter("action");
        if ("send".equals(action)) {
            handleSend(req, resp);
        } else if ("receive".equals(action)) {
            try {
                handleSendParcel(req, resp);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }  else if("showTracking".equals(action)){
            try {
                handleShowTracking(req, resp);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        } else if("list".equals(action)){
            handleList(req, resp);
        }
    }

    private void handleSendParcel(HttpServletRequest req, HttpServletResponse resp) throws SQLException, ClassNotFoundException, IOException {
        resp.setContentType("application/json;charset=UTF-8");
        HttpSession session = req.getSession();
        User user = (User) session.getAttribute("user");
        if (user == null) {
            resp.getWriter().write("请先登录");
            return;
        }
        int userId = user.getUser_id();
        String senderName = req.getParameter("senderName");
        String senderPhone = req.getParameter("senderPhone");
        String senderAddress = req.getParameter("senderAddress");
        String receiverName = req.getParameter("receiverName");
        String receiverPhone = req.getParameter("receiverPhone");
        String receiverAddress = req.getParameter("receiverAddress");
        String parcelType = req.getParameter("parcelType");
        String weight = req.getParameter("weight");
        String price = req.getParameter("price");
        String remark = req.getParameter("remark");
        Parcel parcel = new Parcel();
        parcel.setSenderName(senderName);
        parcel.setSenderPhone(senderPhone);
        parcel.setSenderAddress(senderAddress);
        parcel.setReceiverName(receiverName);
        parcel.setReceiverPhone(receiverPhone);
        parcel.setReceiverAddress(receiverAddress);
        parcel.setParcelType(parcelType);
        parcel.setWeight(weight);
        parcel.setPrice(price);
        parcel.setStatus(0);
        parcel.setUserId(userId);
        parcel.setRemark(remark);
        parcel.setCreateTime(null);
        parcel.setUpdateTime(null);
        ParcelDao dao = new ParcelImpl();
        boolean b = dao.addParcel(parcel, userId);
        if (b) {
            resp.getWriter().write("发货成功");
        } else {
            resp.getWriter().write("发货失败");
        }
    }

    private void handleSend(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        HttpSession session = req.getSession();
        Object username = session.getAttribute("username");
        if (username!=null){
            req.getRequestDispatcher("send.html").forward(req, resp);
        } else {
            req.getRequestDispatcher("index.html").forward(req, resp);
        }
    }
    private void handleShowTracking(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException, SQLException, ClassNotFoundException {
        resp.setContentType("application/json;charset=UTF-8");
        HttpSession session = req.getSession();
        Object username = session.getAttribute("username");
        if (username==null){
            resp.getWriter().write("请先登录");
            return;
        }
        String parcelNo = req.getParameter("parcelNo");
        TrackDao dao = new TrackDaoImpl();
        List<Tracking> trackList = dao.getTrackByParcelNo(parcelNo);
        resp.getWriter().write(JsonUtil.toJson(trackList));
    }
    private void handleList(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        HttpSession session = req.getSession();
        Object username = session.getAttribute("username");
        if (username!=null){
            req.getRequestDispatcher("parcel_list.html").forward(req, resp);
        } else {
            req.getRequestDispatcher("index.html").forward(req, resp);
        }
    }
}
