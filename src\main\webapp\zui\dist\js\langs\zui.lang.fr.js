/*!
 * ZUI: Lang.fr - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */

$.zui.lang('fr', {
    'zui.pager': {
        pageOfText: 'Page {0}',
        prev: 'Prev',
        next: 'Suivant',
        first: 'First',
        last: 'Last',
        goto: 'Goto',
        pageOf: 'Page <strong>{page}</strong>',
        totalPage: '<strong>{totalPage}</strong> pages',
        totalCount: 'Total: <strong>{recTotal}</strong> items',
        pageSize: '<strong>{recPerPage}</strong> per page',
        itemsRange: 'De <strong>{start}</strong> à <strong>{end}</strong>',
        pageOfTotal: 'Page <strong>{page}</strong> de <strong>{totalPage}</strong>'
    },
    'zui.boards': {
        append2end: 'Aller jusqu\'au bout'
    },
    'zui.browser': {
        tip: 'Naviguez sans crainte sur Internet. Mettez votre navigateur à jour dès aujourd\'hui!'
    },
    'zui.calendar': {
        weekNames: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        monthNames: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
        today: 'Aujourd\'hui',
        year: '{0} Année',
        month: '{0} Mois',
        yearMonth: '{0}-{1}'
    },
    'zui.chosenIcons': {
        emptyIcon: '[Aucune icône]',
        commonIcons: 'Icônes communes',
        webIcons: 'Icône Web',
        editorIcons: 'Icône de l\'éditeur',
        directionalIcons: 'Flèche confluence',
        otherIcons: 'Autres icônes',
    },
    'zui.colorPicker': {
        errorTip: "Pas une valeur de couleur valide"
    },
    'zui.datagrid': {
        errorCannotGetDataFromRemote: 'Impossible d\'obtenir les données du serveur distant ({0}).',
        errorCannotHandleRemoteData: 'Impossible de traiter les données renvoyées par le serveur distant.'
    },
    'zui.guideViewer': {
        prevStep: 'Étape précédente',
        nextStep: 'Prochaine étape',
    },
    'zui.tabs': {
        reload: 'Recharger',
        close: 'Fermer',
        closeOthers: 'Fermez les autres onglets',
        closeRight: 'Fermer l\'onglet de droite',
        reopenLast: 'Restaurer le dernier onglet fermé',
        errorCannotFetchFromRemote: 'Impossible d\'obtenir le contenu du serveur distant ({0}).'
    },
    'zui.uploader': {
    },
    datetimepicker: {
        days: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
        daysShort: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        daysMin: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        months: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
        monthsShort: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
        today: "Aujourd\'hui",
        suffix: [],
        meridiem: []
    },
    chosen: {
        no_results_text: 'Pas trouvé'
    },
    bootbox: {
        OK: 'D\'accord',
        CANCEL: 'Annuler',
        CONFIRM: 'Confirmer'
    }
});
