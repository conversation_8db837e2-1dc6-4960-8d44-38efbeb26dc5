/*! KindEditor Copyright (C) kindsoft.net, Licence: http://kindeditor.net/license.php */
/* common */
.ke-inline-block {
  display: -moz-inline-stack;
  display: inline-block;
  vertical-align: middle;
  zoom: 1;

  *display: inline;
  }
.ke-clearfix {
  zoom: 1;
  }
.ke-clearfix:after {
  display: block;
  height: 0;
  clear: both;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
  content: ".";
  }
.ke-shadow {
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .12), 0 1px 3px rgba(0, 0, 0, .1);
          box-shadow: 0 6px 12px rgba(0, 0, 0, .12), 0 1px 3px rgba(0, 0, 0, .1);
  }
/* icons */
[class^="ke-icon-"],
[class*=" ke-icon-"] {
  width: 16px;
  height: 16px;
  }
.ke-icon-source {
  background-position: 0 0;
  }
.ke-icon-preview {
  background-position: 0 -16px;
  }
.ke-icon-print {
  background-position: 0 -32px;
  }
.ke-icon-undo {
  background-position: 0 -48px;
  }
.ke-icon-redo {
  background-position: 0 -64px;
  }
.ke-icon-cut {
  background-position: 0 -80px;
  }
.ke-icon-copy {
  background-position: 0 -96px;
  }
.ke-icon-paste {
  background-position: 0 -112px;
  }
.ke-icon-selectall {
  background-position: 0 -128px;
  }
.ke-icon-justifyleft {
  background-position: 0 -144px;
  }
.ke-icon-justifycenter {
  background-position: 0 -160px;
  }
.ke-icon-justifyright {
  background-position: 0 -176px;
  }
.ke-icon-justifyfull {
  background-position: 0 -192px;
  }
.ke-icon-insertorderedlist {
  background-position: 0 -208px;
  }
.ke-icon-insertunorderedlist {
  background-position: 0 -224px;
  }
.ke-icon-indent {
  background-position: 0 -240px;
  }
.ke-icon-outdent {
  background-position: 0 -256px;
  }
.ke-icon-subscript {
  background-position: 0 -272px;
  }
.ke-icon-superscript {
  background-position: 0 -288px;
  }
.ke-icon-date {
  width: 25px;
  background-position: 0 -304px;
  }
.ke-icon-time {
  width: 25px;
  background-position: 0 -320px;
  }
.ke-icon-formatblock {
  width: 25px;
  background-position: 0 -336px;
  }
.ke-icon-fontname {
  width: 21px;
  background-position: 0 -352px;
  }
.ke-icon-fontsize {
  width: 23px;
  background-position: 0 -368px;
  }
.ke-icon-forecolor {
  width: 20px;
  background-position: 0 -384px;
  }
.ke-icon-hilitecolor {
  width: 23px;
  background-position: 0 -400px;
  }
.ke-icon-bold {
  background-position: 0 -416px;
  }
.ke-icon-italic {
  background-position: 0 -432px;
  }
.ke-icon-underline {
  background-position: 0 -448px;
  }
.ke-icon-strikethrough {
  background-position: 0 -464px;
  }
.ke-icon-removeformat {
  background-position: 0 -480px;
  }
.ke-icon-image {
  background-position: 0 -496px;
  }
.ke-icon-flash {
  background-position: 0 -512px;
  }
.ke-icon-media {
  background-position: 0 -528px;
  }
.ke-icon-div {
  background-position: 0 -544px;
  }
.ke-icon-formula {
  background-position: 0 -576px;
  }
.ke-icon-hr {
  background-position: 0 -592px;
  }
.ke-icon-emoticons {
  background-position: 0 -608px;
  }
.ke-icon-link {
  background-position: 0 -624px;
  }
.ke-icon-unlink {
  background-position: 0 -640px;
  }
.ke-icon-fullscreen {
  background-position: 0 -656px;
  }
.ke-icon-about {
  background-position: 0 -672px;
  }
.ke-icon-plainpaste {
  background-position: 0 -704px;
  }
.ke-icon-wordpaste {
  background-position: 0 -720px;
  }
.ke-icon-table {
  background-position: 0 -784px;
  }
.ke-icon-tablemenu {
  background-position: 0 -768px;
  }
.ke-icon-tableinsert {
  background-position: 0 -784px;
  }
.ke-icon-tabledelete {
  background-position: 0 -800px;
  }
.ke-icon-tablecolinsertleft {
  background-position: 0 -816px;
  }
.ke-icon-tablecolinsertright {
  background-position: 0 -832px;
  }
.ke-icon-tablerowinsertabove {
  background-position: 0 -848px;
  }
.ke-icon-tablerowinsertbelow {
  background-position: 0 -864px;
  }
.ke-icon-tablecoldelete {
  background-position: 0 -880px;
  }
.ke-icon-tablerowdelete {
  background-position: 0 -896px;
  }
.ke-icon-tablecellprop {
  background-position: 0 -912px;
  }
.ke-icon-tableprop {
  background-position: 0 -928px;
  }
.ke-icon-checked {
  background-position: 0 -944px;
  }
.ke-icon-code {
  background-position: 0 -960px;
  }
.ke-icon-map {
  background-position: 0 -976px;
  }
.ke-icon-baidumap {
  background-position: 0 -976px;
  }
.ke-icon-lineheight {
  background-position: 0 -992px;
  }
.ke-icon-clearhtml {
  background-position: 0 -1008px;
  }
.ke-icon-pagebreak {
  background-position: 0 -1024px;
  }
.ke-icon-insertfile {
  background-position: 0 -1040px;
  }
.ke-icon-quickformat {
  background-position: 0 -1056px;
  }
.ke-icon-template {
  background-position: 0 -1072px;
  }
.ke-icon-tablecellsplit {
  background-position: 0 -1088px;
  }
.ke-icon-tablerowmerge {
  background-position: 0 -1104px;
  }
.ke-icon-tablerowsplit {
  background-position: 0 -1120px;
  }
.ke-icon-tablecolmerge {
  background-position: 0 -1136px;
  }
.ke-icon-tablecolsplit {
  background-position: 0 -1152px;
  }
.ke-icon-anchor {
  background-position: 0 -1168px;
  }
.ke-icon-search {
  background-position: 0 -1184px;
  }
.ke-icon-new {
  background-position: 0 -1200px;
  }
.ke-icon-specialchar {
  background-position: 0 -1216px;
  }
.ke-icon-multiimage {
  background-position: 0 -1232px;
  }
/* container */
.ke-container {
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  }
.ke-container.focus {
  border-color: #145ccd;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
  }
.ke-container.ke-loading > .ke-toolbar,
.ke-container.ke-loading > .ke-edit,
.ke-container.ke-loading > .ke-statusbar,
.ke-container.ke-loading + textarea.kindeditor {
  display: none;
  }
/* toolbar */
.ke-toolbar {
  padding: 2px 5px;
  overflow: hidden;
  text-align: left;
  zoom: 1;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  }
.ke-toolbar-icon {
  display: block;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  background-repeat: no-repeat;
  }
.ke-menu .ke-toolbar-icon {
  display: inline-block;
  }
.ke-toolbar-icon-url {
  background-image: url(themes/default/default.png);
  }
.ke-toolbar .ke-outline {
  display: block;
  float: left;
  padding: 1px 2px;
  margin: 1px;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  cursor: pointer;
  filter: alpha(opacity=80);
  border: 1px solid #fff;
  border-radius: 4px;
  opacity: .8;
  }
.ke-toolbar .ke-on,
.ke-toolbar .ke-selected {
  filter: alpha(opacity=100);
  border-color: #ddd;
  opacity: 1;
  }
.ke-toolbar .ke-selected {
  background-color: #f1f1f1;
  }
.ke-toolbar .ke-disabled {
  cursor: default;
  }
.ke-toolbar .ke-separator {
  display: block;
  float: left;
  width: 0;
  height: 16px;
  margin: 2px 3px;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  border-top: 0;
  border-bottom: 0;
  border-left: 1px dotted #ddd;
  }
.ke-toolbar .ke-hr {
  height: 1px;
  overflow: hidden;
  clear: both;
  }
/* edit */
.ke-edit {
  padding: 0;
  }
.ke-edit-iframe,
.ke-edit-textarea {
  padding: 0;
  margin: 0;
  overflow: auto;
  border: 0;
  }
.ke-edit-textarea {
  overflow: auto;
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 12px;
  color: #353535;
  resize: none;
  background-color: #fff;
  }
.ke-edit-textarea:focus {
  outline: none;
  }
/* statusbar */
.ke-statusbar {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 14px;
  height: 14px;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  text-align: center;
  cursor: s-resize;
  background-color: #fff;
  filter: alpha(opacity=0);
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  opacity: 0;
  -webkit-transition: opacity .2s;
       -o-transition: opacity .2s;
          transition: opacity .2s;
  }
.ke-statusbar .ke-statusbar-resize-icon {
  position: relative;
  }
.ke-statusbar .ke-statusbar-resize-icon:before,
.ke-statusbar .ke-statusbar-resize-icon:after {
  position: absolute;
  top: 2px;
  left: -4px;
  display: block;
  width: 0;
  height: 0;
  content: ' ';
  border-color: transparent transparent #808080 transparent;
  border-style: solid;
  border-width: 0 4px 4px 4px;
  }
.ke-statusbar .ke-statusbar-resize-icon:after {
  top: 7px;
  border-color: #808080 transparent transparent transparent;
  border-width: 4px 4px 0 4px;
  }
.ke-statusbar.ke-dragging,
.ke-container:hover .ke-statusbar {
  filter: alpha(opacity=100);
  opacity: 1;
  }
/* menu */
.ke-menu {
  padding: 5px 0;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, 'PingFang SC', 'Source Han Sans CN', 'Source Han Sans', 'Source Han Serif', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  text-align: left;
  background-color: #fff;
  border: 1px solid #cbcbcb;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  }
.ke-menu-item {
  height: 24px;
  overflow: hidden;
  cursor: pointer;
  }
.ke-menu-item-on {
  color: #fff;
  text-decoration: none;
  background-color: #3280fc;
  outline: 0;
  }
.ke-menu-item-left {
  width: 27px;
  overflow: hidden;
  text-align: center;
  }
.ke-menu-item-center {
  width: 0;
  height: 24px;
  border-top: 0;
  border-right: 1px solid #fff;
  border-bottom: 0;
  border-left: 1px solid #e3e3e3;
  }
.ke-menu-item-center-on {
  border-right: 1px solid #e9eff6;
  border-left: 1px solid #e9eff6;
  }
.ke-menu-item-right {
  padding: 0 0 0 5px;
  overflow: hidden;
  line-height: 24px;
  text-align: left;
  border: 0;
  }
.ke-menu-separator {
  height: 0;
  margin: 2px 0;
  overflow: hidden;
  border-top: 1px solid #ccc;
  border-right: 0;
  border-bottom: 1px solid #fff;
  border-left: 0;
  }
/* colorpicker */
.ke-colorpicker {
  background-color: #fff;
  border: 1px solid #cbcbcb;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  }
.ke-colorpicker-table {
  padding: 0;
  margin: 0;
  border-collapse: separate;
  border: 0;
  }
.ke-colorpicker-cell {
  padding: 0;
  font-size: 0;
  line-height: 0;
  cursor: pointer;
  border: none;
  }
.ke-colorpicker-cell-color {
  width: 25px;
  height: 25px;
  padding: 0;
  border: 0;
  }
.ke-colorpicker-cell-top {
  padding: 0;
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, 'PingFang SC', 'Source Han Sans CN', 'Source Han Sans', 'Source Han Serif', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  line-height: 25px;
  text-align: center;
  cursor: pointer;
  }
.ke-colorpicker-cell-on {
  color: #fff;
  background-color: #3280fc;
  }
.ke-colorpicker-cell-on .ke-colorpicker-cell-color {
  border: 2px solid #353535;
  }
.ke-colorpicker-cell-selected .ke-colorpicker-cell-color {
  border: 2px solid #353535;
  }
/* dialog */
.ke-dialog {
  position: absolute;
  padding: 0;
  margin: 0;
  }
.ke-dialog .ke-header {
  width: 100%;
  margin-bottom: 10px;
  }
.ke-dialog .ke-header .ke-left {
  float: left;
  }
.ke-dialog .ke-header .ke-right {
  float: right;
  }
.ke-dialog .ke-header label {
  display: inline;
  margin-right: 0;
  font-weight: normal;
  vertical-align: top;
  cursor: pointer;
  }
.ke-dialog-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  }
.ke-dialog-shadow {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: #f0f0ee;
  border-radius: 4px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
          box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
  }
.ke-dialog-header {
  min-height: 16.53846154px;
  padding: 7.5px 15px;
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, 'PingFang SC', 'Source Han Sans CN', 'Source Han Sans', 'Source Han Serif', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft YaHei', sans-serif;
  font-size: 13px;
  font-weight: bold;
  text-align: left;
  cursor: move;
  background: #f1f1f1;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
  }
.ke-dialog-icon-close {
  position: absolute;
  top: 10px;
  right: 8px;
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url(themes/default/default.png) no-repeat scroll 0 -688px;
  filter: alpha(opacity=60);
  opacity: .6;
  }
.ke-dialog-icon-close:hover {
  filter: alpha(opacity=100);
  opacity: 1;
  }
.ke-dialog-body {
  width: 100%;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, 'PingFang SC', 'Source Han Sans CN', 'Source Han Sans', 'Source Han Serif', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  text-align: left;
  }
.ke-dialog-body textarea {
  display: block;
  padding: 0;
  overflow: auto;
  resize: none;
  }
.ke-dialog-body textarea:focus,
.ke-dialog-body input:focus,
.ke-dialog-body select:focus {
  outline: none;
  }
.ke-dialog-body label {
  display: -moz-inline-stack;
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
  cursor: pointer;
  zoom: 1;

  *display: inline;
  }
.ke-dialog-body img {
  display: -moz-inline-stack;
  display: inline-block;
  vertical-align: middle;
  zoom: 1;

  *display: inline;
  }
.ke-dialog-body select {
  display: -moz-inline-stack;
  display: inline-block;
  width: auto;
  vertical-align: middle;
  zoom: 1;

  *display: inline;
  }
.ke-dialog-body .ke-textarea {
  display: block;
  width: 408px;
  height: 260px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
       -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  }
.ke-dialog-body .ke-form {
  padding: 0;
  margin: 0;
  }
.ke-dialog-loading {
  position: absolute;
  top: 0;
  left: 1px;
  z-index: 1;
  text-align: center;
  }
.ke-dialog-loading-content {
  height: 31px;
  padding-left: 36px;
  font-size: 14px;
  font-weight: bold;
  line-height: 31px;
  color: #666;
  background: url("../themes/common/loading.gif") no-repeat;
  }
.ke-dialog-row {
  margin-bottom: 10px;
  }
.ke-dialog-footer {
  width: 100%;
  padding: 5px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
  }
.ke-dialog-preview,
.ke-dialog-yes {
  margin: 5px;
  }
.ke-dialog-no {
  margin: 5px 10px 5px 5px;
  }
.ke-dialog-mask {
  background-color: #000;
  filter: alpha(opacity=50);
  opacity: .5;
  }
.ke-button-common {
  display: inline-block;
  height: 22px;
  overflow: visible;
  line-height: 21px;
  vertical-align: middle;
  cursor: pointer;
  }
.ke-button-outer {
  position: relative;
  display: -moz-inline-stack;
  display: inline-block;
  padding: 0;
  vertical-align: middle;
  zoom: 1;

  *display: inline;
  }
.ke-button {
  left: 2px;
  padding: 0 12px;
  margin: 0;
  font-size: 12px;
  color: #353535;
  text-decoration: none;
  background-color: #f2f2f2;
  border: 1px solid #bfbfbf;
  border-color: #bfbfbf;
  border-radius: 4px;
  }
.ke-button:hover,
.ke-button:focus,
.ke-button:active,
.ke-button.active,
.open .dropdown-toggle.ke-button {
  color: #353535;
  background-color: #dedede;
  border-color: #a1a1a1;
  -webkit-box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
          box-shadow: 0 2px 1px rgba(0, 0, 0, .1);
  }
.ke-button:active,
.ke-button.active,
.open .dropdown-toggle.ke-button {
  background-color: #ccc;
  background-image: none;
  border-color: #a6a6a6;
  -webkit-box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
          box-shadow: inset 0 4px 6px rgba(0, 0, 0, .15);
  }
.ke-button.disabled,
.ke-button[disabled],
fieldset[disabled] .ke-button,
.ke-button.disabled:hover,
.ke-button[disabled]:hover,
fieldset[disabled] .ke-button:hover,
.ke-button.disabled:focus,
.ke-button[disabled]:focus,
fieldset[disabled] .ke-button:focus,
.ke-button.disabled:active,
.ke-button[disabled]:active,
fieldset[disabled] .ke-button:active,
.ke-button.disabled.active,
.ke-button[disabled].active,
fieldset[disabled] .ke-button.active {
  background-color: #f2f2f2;
  border-color: #bfbfbf;
  }
/* inputbox */
.ke-input-text {
  display: -moz-inline-stack;
  display: inline-block;
  height: 22px;
  padding: 0 4px;
  font-family: "sans serif", tahoma, verdana, helvetica;
  font-size: 12px;
  line-height: 22px;
  vertical-align: middle;
  zoom: 1;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
       -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;

  *display: inline;
  }
.ke-input-text:focus,
.ke-input-text.focus {
  border-color: #145ccd;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
  }
.ke-input-text[disabled] {
  background: #eee;
  }
.ke-input-number {
  width: 50px;
  }
.ke-input-color,
ke-dialog-content select {
  display: -moz-inline-stack;
  display: inline-block;
  width: 60px;
  height: 20px;
  padding-left: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  vertical-align: middle;
  cursor: pointer;
  zoom: 1;
  background-color: #fff;
  border: 1px solid #ccc;

  *display: inline;
  }
.ke-upload-button {
  position: relative;
  }
.ke-upload-area {
  position: relative;
  padding: 0;
  margin: 0;
  overflow: hidden;
  }
.ke-upload-area .ke-upload-file {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 811212;
  padding: 0;
  margin: 0;
  font-size: 60px;
  filter: alpha(opacity=0);
  border: 0 none;
  opacity: 0;
  }
/* tabs */
.ke-tabs {
  padding-left: 5px;
  margin-bottom: 20px;
  font: 12px/1 "sans serif", tahoma, verdana, helvetica;
  border-bottom: 1px solid #a0a0a0;
  }
.ke-tabs-ul {
  padding: 0;
  margin: 0;
  list-style-position: outside;
  list-style-type: none;
  list-style-image: none;
  }
.ke-tabs-li {
  position: relative;
  float: left;
  padding: 0 20px;
  margin: 0 2px -1px 0;
  line-height: 25px;
  color: #555;
  text-align: center;
  cursor: pointer;
  background-color: #f0f0ee;
  border: 1px solid #a0a0a0;
  }
.ke-tabs-li-selected {
  color: #000;
  cursor: default;
  background-color: #fff;
  border-bottom: 1px solid #fff;
  }
.ke-tabs-li-on {
  color: #000;
  background-color: #fff;
  }
/* progressbar */
.ke-progressbar {
  position: relative;
  padding: 0;
  margin: 0;
  }
.ke-progressbar-bar {
  width: 80px;
  height: 5px;
  padding: 0;
  margin: 10px 10px 0 10px;
  border: 1px solid #6fa5db;
  }
.ke-progressbar-bar-inner {
  width: 0;
  height: 5px;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background-color: #6fa5db;
  }
.ke-progressbar-percent {
  position: absolute;
  top: 0;
  left: 40%;
  display: none;
  }
/* swfupload */
.ke-swfupload-top {
  position: relative;
  margin-bottom: 10px;

  _width: 608px;
  }
.ke-swfupload-button {
  height: 23px;
  line-height: 23px;
  }
.ke-swfupload-desc {
  height: 23px;
  padding: 0 10px;
  line-height: 23px;
  }
.ke-swfupload-startupload {
  position: absolute;
  top: 0;
  right: 0;
  }
.ke-swfupload-body {
  width: auto;
  height: 370px;
  padding: 5px;
  overflow: scroll;
  background-color: #fff;
  border: 1px solid #ccc;
  }
.ke-swfupload-body .ke-item {
  width: 100px;
  margin: 5px;
  }
.ke-swfupload-body .ke-photo {
  position: relative;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  }
.ke-swfupload-body .ke-delete {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url(themes/default/default.png) no-repeat scroll 0 -688px;
  }
.ke-swfupload-body .ke-status {
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100px;
  height: 17px;
  }
.ke-swfupload-body .ke-message {
  width: 100px;
  height: 17px;
  overflow: hidden;
  text-align: center;
  }
.ke-swfupload-body .ke-error {
  color: red;
  }
.ke-swfupload-body .ke-name {
  width: 100px;
  height: 16px;
  overflow: hidden;
  text-align: center;
  }
.ke-swfupload-body .ke-on {
  background-color: #e9eff6;
  border: 1px solid #5690d2;
  }
/* emoticons */
.ke-plugin-emoticons {
  position: relative;
  }
.ke-plugin-emoticons .ke-preview {
  position: absolute;
  top: 0;
  display: none;
  padding: 10px;
  margin: 2px;
  text-align: center;
  background-color: #fff;
  border: 1px solid #a0a0a0;
  }
.ke-plugin-emoticons .ke-preview-img {
  padding: 0;
  margin: 0;
  border: 0;
  }
.ke-plugin-emoticons .ke-table {
  padding: 0;
  margin: 0;
  border-collapse: separate;
  border: 0;
  }
.ke-plugin-emoticons .ke-cell {
  padding: 1px;
  margin: 0;
  cursor: pointer;
  border: 1px solid #f0f0ee;
  }
.ke-plugin-emoticons .ke-on {
  background-color: #e9eff6;
  border: 1px solid #5690d2;
  }
.ke-plugin-emoticons .ke-img {
  display: block;
  width: 24px;
  height: 24px;
  padding: 0;
  margin: 2px;
  margin: 0;
  overflow: hidden;
  background-repeat: no-repeat;
  border: 0;
  }
.ke-plugin-emoticons .ke-page {
  padding: 0;
  margin: 5px;
  font: 12px/1 "sans serif", tahoma, verdana, helvetica;
  color: #333;
  text-align: right;
  text-decoration: none;
  border: 0;
  }
.ke-plugin-plainpaste-textarea,
.ke-plugin-wordpaste-iframe {
  display: block;
  width: 408px;
  height: 260px;
  font-family: "sans serif", tahoma, verdana, helvetica;
  font-size: 12px;
  border: 1px solid #ccc;
  }
/* filemanager */
.ke-plugin-filemanager-header {
  width: 100%;
  margin-bottom: 10px;
  }
.ke-plugin-filemanager-header .ke-left {
  float: left;
  }
.ke-plugin-filemanager-header .ke-right {
  float: right;
  }
.ke-plugin-filemanager-body {
  width: auto;
  height: 370px;
  padding: 5px;
  overflow: scroll;
  background-color: #fff;
  border: 1px solid #ccc;
  }
.ke-plugin-filemanager-body .ke-item {
  width: 100px;
  margin: 5px;
  }
.ke-plugin-filemanager-body .ke-photo {
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  }
.ke-plugin-filemanager-body .ke-name {
  width: 100px;
  height: 16px;
  overflow: hidden;
  text-align: center;
  }
.ke-plugin-filemanager-body .ke-on {
  background-color: #e9eff6;
  border: 1px solid #5690d2;
  }
.ke-plugin-filemanager-body .ke-table {
  width: 95%;
  padding: 0;
  margin: 0;
  border-collapse: separate;
  border: 0;
  }
.ke-plugin-filemanager-body .ke-table .ke-cell {
  padding: 0;
  margin: 0;
  border: 0;
  }
.ke-plugin-filemanager-body .ke-table .ke-name {
  width: 55%;
  text-align: left;
  }
.ke-plugin-filemanager-body .ke-table .ke-size {
  width: 15%;
  text-align: left;
  }
.ke-plugin-filemanager-body .ke-table .ke-datetime {
  width: 30%;
  text-align: center;
  }
.ke-dialog input[type=number]::-webkit-outer-spin-button,
.ke-dialog input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
  }
.ke-dialog input[type=number] {
  -moz-appearance: textfield;
  }
