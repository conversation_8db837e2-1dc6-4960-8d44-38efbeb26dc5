/*!
 * ZUI: 图片裁剪工具 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */.img-cutter{padding:10px;margin-bottom:20px;background:#e5e5e5;border:1px solid #ddd}.img-cutter>.canvas{position:relative;min-width:50px;max-width:100%;margin:0 auto}.img-cutter>.canvas>.cover{position:absolute;top:0;left:0;z-index:1;width:100%;height:100%;-webkit-transition:all .4s cubic-bezier(.175,.885,.32,1);-o-transition:all .4s cubic-bezier(.175,.885,.32,1);transition:all .4s cubic-bezier(.175,.885,.32,1)}.img-cutter>.canvas>img{-webkit-transition:all .4s cubic-bezier(.175,.885,.32,1);-o-transition:all .4s cubic-bezier(.175,.885,.32,1);transition:all .4s cubic-bezier(.175,.885,.32,1)}.img-cutter>.canvas>.controller{position:absolute;top:5%;left:5%;z-index:5;width:100px;height:100px;cursor:move;background:0 0;border:1px dashed #fff;border-color:rgba(255,255,255,.7);-webkit-transition:opacity .4s cubic-bezier(.175,.885,.32,1);-o-transition:opacity .4s cubic-bezier(.175,.885,.32,1);transition:opacity .4s cubic-bezier(.175,.885,.32,1)}.img-cutter>.canvas>.controller>.control{position:absolute;width:6px;height:6px;background:#000;background:rgba(0,0,0,.6);border:1px solid #fff;border-color:rgba(255,255,255,.6)}.img-cutter>.canvas>.controller>.control[data-direction=left]{top:50%;left:-4px;margin-top:-3px;cursor:w-resize}.img-cutter>.canvas>.controller>.control[data-direction=top]{top:-4px;left:50%;margin-left:-3px;cursor:n-resize}.img-cutter>.canvas>.controller>.control[data-direction=right]{top:50%;right:-4px;margin-top:-3px;cursor:e-resize}.img-cutter>.canvas>.controller>.control[data-direction=bottom]{bottom:-4px;left:50%;margin-left:-3px;cursor:s-resize}.img-cutter>.canvas>.controller>.control[data-direction=top-left]{top:-4px;left:-4px;cursor:nw-resize}.img-cutter>.canvas>.controller>.control[data-direction=top-right]{top:-4px;right:-4px;cursor:ne-resize}.img-cutter>.canvas>.controller>.control[data-direction=bottom-left]{bottom:-4px;left:-4px;cursor:sw-resize}.img-cutter>.canvas>.controller>.control[data-direction=bottom-right]{right:-4px;bottom:-4px;cursor:se-resize}.img-cutter>.canvas>.cliper{position:absolute;top:0;left:0;z-index:2;width:100%;height:100%;clip:rect(0,50px,50px,0)}.img-cutter.hover>.canvas>.controller>.cover,.img-cutter.hover>.canvas>img{filter:alpha(opacity=0);opacity:0}.img-cutter.hover>.canvas>.controller{display:none}