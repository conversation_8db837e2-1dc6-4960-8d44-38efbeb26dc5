package com.ruoyi.dao.impl;

import com.ruoyi.dao.UserDao;
import com.ruoyi.entity.User;
import com.ruoyi.jdbc.JdbcUtil;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class UserDaoImpl implements UserDao {
    @Override
    public User getUserByUsernameAndPwd(String username, String pwd) throws SQLException, ClassNotFoundException {
        String sql = "select * from t_user where username = ? and pwd = ?";
        User user = new User();
        Connection connection = JdbcUtil.getConnection();
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, username);
        preparedStatement.setString(2, pwd);
        ResultSet resultSet = preparedStatement.executeQuery();
        while (resultSet.next()){
            user.setUser_id(resultSet.getInt("user_id"));
            user.setUsername(resultSet.getString("username"));
            user.setPwd(resultSet.getString("pwd"));
        }
        JdbcUtil.close(connection, preparedStatement, resultSet);
        return user;
    }

    @Override
    public boolean registerByUsernameAndPwd(String username, String pwd) throws SQLException, ClassNotFoundException {
        String sql="insert into t_user(username,pwd) values(?,?)";
        Connection connection = JdbcUtil.getConnection();
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, username);
        preparedStatement.setString(2, pwd);
        int i = preparedStatement.executeUpdate();
        if (i>0) return true;
        return false;
    }
}
