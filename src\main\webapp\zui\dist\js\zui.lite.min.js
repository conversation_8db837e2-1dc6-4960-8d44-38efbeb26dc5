/*!
 * ZUI: Lite edition - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
/*! Some code copy from Bootstrap v3.0.0 by @fat and @mdo. (Copyright 2013 Twitter, Inc. Licensed under http://www.apache.org/licenses/)*/
!function(t,e,i){"use strict";if("undefined"==typeof t)throw new Error("ZUI requires jQuery");t.zui||(t.zui=function(e){t.isPlainObject(e)&&t.extend(t.zui,e)});var n={all:-1,left:0,middle:1,right:2},o=0;t.zui({uuid:function(t){var e=1e5*(Date.now()-1580890015292)+10*Math.floor(1e4*Math.random())+o++%10;return t?e:e.toString(36)},callEvent:function(t,e,n){if("function"==typeof t){n!==i&&(t=t.bind(n));var o=t(e);return e&&(e.result=o),!(o!==i&&!o)}return 1},strCode:function(t){var e=0;if("string"!=typeof t&&(t=String(t)),t&&t.length)for(var i=0;i<t.length;++i)e+=(i+1)*t.charCodeAt(i);return e},getMouseButtonCode:function(t){return"number"!=typeof t&&(t=n[t]),t!==i&&null!==t||(t=-1),t},defaultLang:"en",clientLang:function(){var i,n=e.config;if("undefined"!=typeof n&&n.clientLang&&(i=n.clientLang),!i){var o=t("html").attr("lang");i=o?o:navigator.userLanguage||navigator.userLanguage||t.zui.defaultLang}return i.replace("-","_").toLowerCase()},langDataMap:{},addLangData:function(e,i,n){var o={};n&&i&&e?(o[i]={},o[i][e]=n):e&&i&&!n?(n=i,t.each(n,function(t){o[t]={},o[t][e]=n[t]})):!e||i||n||t.each(n,function(e){var i=n[e];t.each(i,function(t){o[t]||(o[t]={}),o[t][e]=i[t]})}),t.extend(!0,t.zui.langDataMap,o)},getLangData:function(e,i,n){if(!arguments.length)return t.extend({},t.zui.langDataMap);if(1===arguments.length)return t.extend({},t.zui.langDataMap[e]);if(2===arguments.length){var o=t.zui.langDataMap[e];return o?i?o[i]:o:{}}if(3===arguments.length){i=i||t.zui.clientLang();var o=t.zui.langDataMap[e],a=o?o[i]:{};return t.extend(!0,{},n[i]||n.en||n.zh_cn,a)}return null},lang:function(){return arguments.length&&t.isPlainObject(arguments[arguments.length-1])?t.zui.addLangData.apply(null,arguments):t.zui.getLangData.apply(null,arguments)},_scrollbarWidth:0,checkBodyScrollbar:function(){if(document.body.clientWidth>=e.innerWidth)return 0;if(!t.zui._scrollbarWidth){var i=document.createElement("div");i.className="scrollbar-measure",document.body.appendChild(i),t.zui._scrollbarWidth=i.offsetWidth-i.clientWidth,document.body.removeChild(i)}return t.zui._scrollbarWidth},fixBodyScrollbar:function(){if(t.zui.checkBodyScrollbar()){var e=t("body"),i=parseInt(e.css("padding-right")||0,10);return t.zui._scrollbarWidth&&e.css({paddingRight:i+t.zui._scrollbarWidth,overflowY:"hidden"}),!0}},resetBodyScrollbar:function(){t("body").css({paddingRight:"",overflowY:""})}}),t.fn.callEvent=function(e,n,o){var a=t(this),r=e.indexOf(".zui."),s=r<0?e:e.substring(0,r),l=t.Event(s,n);if(o===i&&r>0&&(o=a.data(e.substring(r+1))),o&&o.options){var d=o.options[s];"function"==typeof d&&(l.result=t.zui.callEvent(d,l,o))}return a.trigger(l),l},t.fn.callComEvent=function(t,e,n){n===i||Array.isArray(n)||(n=[n]);var o,a=this;a.trigger(e,n);var r=t.options[e];return r&&(o=r.apply(t,n)),o}}(jQuery,window,void 0),function(t){"use strict";t.fn.fixOlPd=function(e){return e=e||10,this.each(function(){var i=t(this);i.css("paddingLeft",Math.ceil(Math.log10(i.children().length))*e+10)})},t(function(){t(".ol-pd-fix,.article ol").fixOlPd()})}(jQuery),+function(t){"use strict";var e='[data-dismiss="alert"]',i="zui.alert",n=function(i){t(i).on("click",e,this.close)};n.prototype.close=function(e){function n(){r.trigger("closed."+i).remove()}var o=t(this),a=o.attr("data-target");a||(a=o.attr("href"),a=a&&a.replace(/.*(?=#[^\s]*$)/,""));var r=t(a);e&&e.preventDefault(),r.length||(r=o.hasClass("alert")?o:o.parent()),r.trigger(e=t.Event("close."+i)),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one(t.support.transition.end,n).emulateTransitionEnd(150):n())};var o=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var o=t(this),a=o.data(i);a||o.data(i,a=new n(this)),"string"==typeof e&&a[e].call(o)})},t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=o,this},t(document).on("click."+i+".data-api",e,n.prototype.close)}(window.jQuery),+function(t){"use strict";function e(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;t(this).one("bsTransitionEnd",function(){i=!0});var o=function(){i||t(n).trigger(t.support.transition.end)};return setTimeout(o,e),this},t(function(){t.support.transition=e(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),+function(t){"use strict";var e="zui.collapse",i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.transitioning=null,this.options.parent&&(this.$parent=t(this.options.parent)),this.options.toggle&&this.toggle()};i.DEFAULTS={toggle:!0},i.prototype.dimension=function(){var t=this.$element.hasClass("width");return t?"width":"height"},i.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var i=t.Event("show."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var n=this.$parent&&this.$parent.find(".in");if(n&&n.length){var o=n.data(e);if(o&&o.transitioning)return;n.collapse("hide"),o||n.data(e,null)}var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0),this.transitioning=1;var r=function(){this.$element.removeClass("collapsing").addClass("in")[a]("auto"),this.transitioning=0,this.$element.trigger("shown."+e)};if(!t.support.transition)return r.call(this);var s=t.camelCase(["scroll",a].join("-"));this.$element.one(t.support.transition.end,r.bind(this)).emulateTransitionEnd(350)[a](this.$element[0][s])}}},i.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var i=t.Event("hide."+e);if(this.$element.trigger(i),!i.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var o=function(){this.transitioning=0,this.$element.trigger("hidden."+e).removeClass("collapsing").addClass("collapse")};return t.support.transition?void this.$element[n](0).one(t.support.transition.end,o.bind(this)).emulateTransitionEnd(350):o.call(this)}}},i.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var n=t.fn.collapse;t.fn.collapse=function(n){return this.each(function(){var o=t(this),a=o.data(e),r=t.extend({},i.DEFAULTS,o.data(),"object"==typeof n&&n);a||o.data(e,a=new i(this,r)),"string"==typeof n&&a[n]()})},t.fn.collapse.Constructor=i,t.fn.collapse.noConflict=function(){return t.fn.collapse=n,this},t(document).on("click."+e+".data-api","[data-toggle=collapse]",function(i){var n,o=t(this),a=o.attr("data-target")||i.preventDefault()||(n=o.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,""),r=t(a),s=r.data(e),l=s?"toggle":o.data(),d=o.attr("data-parent"),h=d&&t(d);s&&s.transitioning||(h&&h.find('[data-toggle=collapse][data-parent="'+d+'"]').not(o).addClass("collapsed"),o[r.hasClass("in")?"addClass":"removeClass"]("collapsed")),r.collapse(l)})}(window.jQuery),function(t,e){"use strict";var i=1200,n=992,o=768,a=e(t),r=function(){var t=a.width();e("html").toggleClass("screen-desktop",t>=n&&t<i).toggleClass("screen-desktop-wide",t>=i).toggleClass("screen-tablet",t>=o&&t<n).toggleClass("screen-phone",t<o).toggleClass("device-mobile",t<n).toggleClass("device-desktop",t>=n)},s="",l=navigator.userAgent;l.match(/(iPad|iPhone|iPod)/i)?s+=" os-ios":l.match(/android/i)?s+=" os-android":l.match(/Win/i)?s+=" os-windows":l.match(/Mac/i)?s+=" os-mac":l.match(/Linux/i)?s+=" os-linux":l.match(/X11/i)&&(s+=" os-unix"),"ontouchstart"in document.documentElement&&(s+=" is-touchable"),e("html").addClass(s),a.resize(r),r()}(window,jQuery),function(t){"use strict";var e={zh_cn:'您的浏览器版本过低，无法体验所有功能，建议升级或者更换浏览器。 <a href="https://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',zh_tw:'您的瀏覽器版本過低，無法體驗所有功能，建議升級或者更换瀏覽器。<a href="https://browsehappy.com/" target="_blank" class="alert-link">了解更多...</a>',en:'Your browser is too old, it has been unable to experience the colorful internet. We strongly recommend that you upgrade a better one. <a href="https://browsehappy.com/" target="_blank" class="alert-link">Learn more...</a>'},i=function(){for(var t=!1,e=11;e>5;e--)if(this.isIE(e)){t=e;break}this.ie=t,this.cssHelper()};i.prototype.cssHelper=function(){var e=this.ie,i=t("html");i.toggleClass("ie",e).removeClass("ie-6 ie-7 ie-8 ie-9 ie-10"),e&&i.addClass("ie-"+e).toggleClass("gt-ie-7 gte-ie-8 support-ie",e>=8).toggleClass("lte-ie-7 lt-ie-8 outdated-ie",e<8).toggleClass("gt-ie-8 gte-ie-9",e>=9).toggleClass("lte-ie-8 lt-ie-9",e<9).toggleClass("gt-ie-9 gte-ie-10",e>=10).toggleClass("lte-ie-9 lt-ie-10",e<10).toggleClass("gt-ie-10 gte-ie-11",e>=11).toggleClass("lte-ie-10 lt-ie-11",e<11)},i.prototype.tip=function(i){var n=t("#browseHappyTip");n.length||(n=t('<div id="browseHappyTip" class="alert alert-dismissable alert-danger-inverse alert-block" style="position: relative; z-index: 99999"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><div class="container"><div class="content text-center"></div></div></div>'),n.prependTo("body")),i||(i=t.zui.getLangData("zui.browser",t.zui.clientLang(),e),"object"==typeof i&&(i=i.tip)),n.find(".content").html(i)},i.prototype.isIE=function(t){if(11===t)return this.isIE11();if(10===t)return this.isIE10();if(!t&&(this.isIE11()||this.isIE10()))return!0;var e=document.createElement("b");return e.innerHTML="<!--[if IE "+(t||"")+"]><i></i><![endif]-->",1===e.getElementsByTagName("i").length},i.prototype.isIE10=function(){return navigator.appVersion.indexOf("MSIE 10")!==-1},i.prototype.isIE11=function(){var t=navigator.userAgent;return t.indexOf("Trident")!==-1&&t.indexOf("rv:11")!==-1},t.zui({browser:new i}),t(function(){t("body").hasClass("disabled-browser-tip")||t.zui.browser.ie&&t.zui.browser.ie<8&&t.zui.browser.tip()})}(jQuery),function(t){"use strict";var e=864e5,i=function(t){return t instanceof Date||("number"==typeof t&&t<1e10&&(t*=1e3),t=new Date(t)),t},n=function(t){return i(t).getTime()},o=function(t,e){t=i(t),void 0===e&&(e="yyyy-MM-dd hh:mm:ss");var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),"S+":t.getMilliseconds()};/(y+)/i.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(var o in n)new RegExp("("+o+")").test(e)&&(e=e.replace(RegExp.$1,1==RegExp.$1.length?n[o]:("00"+n[o]).substr((""+n[o]).length)));return e},a=function(t,e){return t.setTime(t.getTime()+e),t},r=function(t,i){return a(t,i*e)},s=function(t){return new Date(i(t).getTime())},l=function(t){return t%4===0&&t%100!==0||t%400===0},d=function(t,e){return[31,l(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]},h=function(t){return d(t.getFullYear(),t.getMonth())},c=function(t){return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t},u=function(t,e){var i=t.getDate();return t.setDate(1),t.setMonth(t.getMonth()+e),t.setDate(Math.min(i,h(t))),t},p=function(t,e){e=e||1;for(var i=new Date(t.getTime());i.getDay()!=e;)i=r(i,-1);return c(i)},f=function(t,e){return t.toDateString()===e.toDateString()},g=function(t,e){var i=p(t),n=r(s(i),7);return e>=i&&e<n},m=function(t,e){return t.getFullYear()===e.getFullYear()},v={formatDate:o,createDate:i,date:{ONEDAY_TICKS:e,create:i,getTimestamp:n,format:o,addMilliseconds:a,addDays:r,cloneDate:s,isLeapYear:l,getDaysInMonth:d,getDaysOfThisMonth:h,clearTime:c,addMonths:u,getLastWeekday:p,isSameDay:f,isSameWeek:g,isSameYear:m}};t.$&&t.$.zui?$.zui(v):t.dateHelper=v.date,t.noDatePrototypeHelper||(Date.ONEDAY_TICKS=e,Date.prototype.format||(Date.prototype.format=function(t){return o(this,t)}),Date.prototype.addMilliseconds||(Date.prototype.addMilliseconds=function(t){return a(this,t)}),Date.prototype.addDays||(Date.prototype.addDays=function(t){return r(this,t)}),Date.prototype.clone||(Date.prototype.clone=function(){return s(this)}),Date.isLeapYear||(Date.isLeapYear=function(t){return l(t)}),Date.getDaysInMonth||(Date.getDaysInMonth=function(t,e){return d(t,e)}),Date.prototype.isLeapYear||(Date.prototype.isLeapYear=function(){return l(this.getFullYear())}),Date.prototype.clearTime||(Date.prototype.clearTime=function(){return c(this)}),Date.prototype.getDaysInMonth||(Date.prototype.getDaysInMonth=function(){return h(this)}),Date.prototype.addMonths||(Date.prototype.addMonths=function(t){return u(this,t)}),Date.prototype.getLastWeekday||(Date.prototype.getLastWeekday=function(t){return p(this,t)}),Date.prototype.isSameDay||(Date.prototype.isSameDay=function(t){return f(t,this)}),Date.prototype.isSameWeek||(Date.prototype.isSameWeek=function(t){return g(t,this)}),Date.prototype.isSameYear||(Date.prototype.isSameYear=function(t){return m(this,t)}),Date.create||(Date.create=function(t){return i(t)}),Date.timestamp||(Date.timestamp=function(t){return n(t)}))}(window),function(){"use strict";var t=function(t,e){if(arguments.length>1){var i;if(2==arguments.length&&"object"==typeof e)for(var n in e)void 0!==e[n]&&(i=new RegExp("({"+n+"})","g"),t=t.replace(i,e[n]));else for(var o=1;o<arguments.length;o++)void 0!==arguments[o]&&(i=new RegExp("({["+(o-1)+"]})","g"),t=t.replace(i,arguments[o]))}return t},e=function(t){if(null!==t){var e,i;return i=/\d*/i,e=t.match(i),e==t}return!1},i={formatString:t,string:{format:t,isNum:e}};window.$&&window.$.zui?$.zui(i):window.stringHelper=i.string,window.noStringPrototypeHelper||(String.prototype.format||(String.prototype.format=function(){var e=[].slice.call(arguments);return e.unshift(this),t.apply(this,e)}),String.prototype.isNum||(String.prototype.isNum=function(){return e(this)}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){return(void 0===e||e>this.length)&&(e=this.length),this.substring(e-t.length,e)===t}),String.prototype.startsWith||Object.defineProperty(String.prototype,"startsWith",{value:function(t,e){return e=!e||e<0?0:+e,this.substring(e,e+t.length)===t}}),String.prototype.includes||(String.prototype.includes=function(){return String.prototype.indexOf.apply(this,arguments)!==-1}))}(),/*!
 * jQuery resize event - v1.1
 * http://benalman.com/projects/jquery-resize-plugin/
 * Copyright (c) 2010 "Cowboy" Ben Alman
 * MIT & GPL http://benalman.com/about/license/
 */
function(t,e,i){"$:nomunge";function n(){o=e[s](function(){a.each(function(){var e=t(this),i=e.width(),n=e.height(),o=t.data(this,d);i===o.w&&n===o.h||e.trigger(l,[o.w=i,o.h=n])}),n()},r[h])}var o,a=t([]),r=t.resize=t.extend(t.resize,{}),s="setTimeout",l="resize",d=l+"-special-event",h="delay",c="throttleWindow";r[h]=250,r[c]=!0,t.event.special[l]={setup:function(){if(!r[c]&&this[s])return!1;var e=t(this);a=a.add(e),t.data(this,d,{w:e.width(),h:e.height()}),1===a.length&&n()},teardown:function(){if(!r[c]&&this[s])return!1;var e=t(this);a=a.not(e),e.removeData(d),a.length||clearTimeout(o)},add:function(e){function n(e,n,a){var r=t(this),s=t.data(this,d)||{};s.w=n!==i?n:r.width(),s.h=a!==i?a:r.height(),o.apply(this,arguments)}if(!r[c]&&this[s])return!1;var o;return"function"==typeof e?(o=e,n):(o=e.handler,void(e.handler=n))}}}(jQuery,this),function(t,e){"use strict";var i,n,o="localStorage",a="page_"+t.location.pathname+t.location.search,r=function(){this.silence=!0;try{o in t&&t[o]&&t[o].setItem&&(this.enable=!0,i=t[o])}catch(r){}this.enable||(n={},i={getLength:function(){var t=0;return e.each(n,function(){t++}),t},key:function(t){var i,o=0;return e.each(n,function(e){return o===t?(i=e,!1):void o++}),i},removeItem:function(t){delete n[t]},getItem:function(t){return n[t]},setItem:function(t,e){n[t]=e},clear:function(){n={}}}),this.storage=i,this.page=this.get(a,{})};r.prototype.pageSave=function(){if(e.isEmptyObject(this.page))this.remove(a);else{var t,i=[];for(t in this.page){var n=this.page[t];null===n&&i.push(t)}for(t=i.length-1;t>=0;t--)delete this.page[i[t]];this.set(a,this.page)}},r.prototype.pageRemove=function(t){"undefined"!=typeof this.page[t]&&(this.page[t]=null,this.pageSave())},r.prototype.pageClear=function(){this.page={},this.pageSave()},r.prototype.pageGet=function(t,e){var i=this.page[t];return void 0===e||null!==i&&void 0!==i?i:e},r.prototype.pageSet=function(t,i){e.isPlainObject(t)?e.extend(!0,this.page,t):this.page[this.serialize(t)]=i,this.pageSave()},r.prototype.check=function(){if(!this.enable&&!this.silence)throw new Error("Browser not support localStorage or enable status been set true.");return this.enable},r.prototype.length=function(){return this.check()?i.getLength?i.getLength():i.length:0},r.prototype.removeItem=function(t){return i.removeItem(t),this},r.prototype.remove=function(t){return this.removeItem(t)},r.prototype.getItem=function(t){return i.getItem(t)},r.prototype.get=function(t,e){var i=this.deserialize(this.getItem(t));return"undefined"!=typeof i&&null!==i||"undefined"==typeof e?i:e},r.prototype.key=function(t){return i.key(t)},r.prototype.setItem=function(t,e){return i.setItem(t,e),this},r.prototype.set=function(t,e){return void 0===e?this.remove(t):(this.setItem(t,this.serialize(e)),this)},r.prototype.clear=function(){return i.clear(),this},r.prototype.forEach=function(t){for(var e=this.length(),n=e-1;n>=0;n--){var o=i.key(n);t(o,this.get(o))}return this},r.prototype.getAll=function(){var t={};return this.forEach(function(e,i){t[e]=i}),t},r.prototype.serialize=function(t){return"string"==typeof t?t:JSON.stringify(t)},r.prototype.deserialize=function(t){if("string"==typeof t)try{return JSON.parse(t)}catch(e){return t||void 0}},e.zui({store:new r})}(window,jQuery),+function(t){"use strict";var e="zui.tab",i=function(e){this.element=t(e)};i.prototype.show=function(){var i=this.element,n=i.closest("ul:not(.dropdown-menu)"),o=i.attr("data-target")||i.attr("data-tab");if(o||(o=i.attr("href"),o=o&&o.replace(/.*(?=#[^\s]*$)/,"")),!i.parent("li").hasClass("active")){var a=n.find(".active:last a")[0],r=t.Event("show."+e,{relatedTarget:a});if(i.trigger(r),!r.isDefaultPrevented()){var s=t(o);this.activate(i.parent("li"),n),this.activate(s,s.parent(),function(){i.trigger({type:"shown."+e,relatedTarget:a})})}}},i.prototype.activate=function(e,i,n){function o(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),n&&n()}var a=i.find("> .active"),r=n&&t.support.transition&&a.hasClass("fade");r?a.one(t.support.transition.end,o).emulateTransitionEnd(150):o(),a.removeClass("in")};var n=t.fn.tab;t.fn.tab=function(n){return this.each(function(){var o=t(this),a=o.data(e);a||o.data(e,a=new i(this)),"string"==typeof n&&a[n]()})},t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this},t(document).on("click.zui.tab.data-api",'[data-toggle="tab"], [data-tab]',function(e){e.preventDefault(),t(this).tab("show")})}(window.jQuery),+function(t,e){"use strict";function i(e,i,a){return this.each(function(){var r=t(this),s=r.data(n),l=t.extend({},o.DEFAULTS,r.data(),"object"==typeof e&&e);s||r.data(n,s=new o(this,l)),"string"==typeof e?s[e](i,a):l.show&&s.show(i,a)})}var n="zui.modal",o=function(i,o){var a=this;a.options=o,a.$body=t(document.body),a.$element=t(i),a.$backdrop=a.isShown=null,a.scrollbarWidth=0,o.moveable===e&&(a.options.moveable=a.$element.hasClass("modal-moveable")),o.remote&&a.$element.find(".modal-content").load(o.remote,function(){a.$element.trigger("loaded."+n)}),o.scrollInside&&t(window).on("resize."+n,function(){a.isShown&&a.adjustPosition(e,100)})};o.VERSION="3.2.0",o.TRANSITION_DURATION=300,o.BACKDROP_TRANSITION_DURATION=150,o.DEFAULTS={backdrop:!0,keyboard:!0,show:!0,position:"fit"};var a=function(e,i){var n=t(window);i.left=Math.max(0,Math.min(i.left,n.width()-e.outerWidth())),i.top=Math.max(0,Math.min(i.top,n.height()-e.outerHeight())),e.css(i)};o.prototype.toggle=function(t,e){return this.isShown?this.hide():this.show(t,e)},o.prototype.adjustPosition=function(i,o){var r=this;if(clearTimeout(r.reposTask),o)return void(r.reposTask=setTimeout(r.adjustPosition.bind(r,i,0),o));var s=r.options;if(i===e&&(i=s.position),i!==e&&null!==i){"function"==typeof i&&(i=i(r));var l=r.$element.find(".modal-dialog"),d=t(window).height(),h={maxHeight:"initial",overflow:"visible"},c=l.find(".modal-body").css(h);if(s.scrollInside&&c.length){var u=s.headerHeight,p=s.footerHeight,f=l.find(".modal-header"),g=l.find(".modal-footer");"number"!=typeof u&&(u=f.length?f.outerHeight():"function"==typeof u?u(f):0),"number"!=typeof p&&(p=g.length?g.outerHeight():"function"==typeof p?p(g):0),h.maxHeight=d-u-p,h.overflow=c[0].scrollHeight>h.maxHeight?"auto":"visible",c.css(h)}var m=Math.max(0,(d-l.outerHeight())/2);if("fit"===i?i={top:m>50?Math.floor(2*m/3):m}:"center"===i?i={top:m}:t.isPlainObject(i)||(i={top:i}),l.hasClass("modal-moveable")){var v=null,y=s.rememberPos;y&&(y===!0?v=r.$element.data("modal-pos"):t.zui.store&&(v=t.zui.store.pageGet(n+".rememberPos."+y))),i=t.extend(i,{left:Math.max(0,(t(window).width()-l.outerWidth())/2)},v),"inside"===s.moveable?a(l,i):l.css(i)}else l.css(i)}},o.prototype.setMoveable=function(){t.fn.draggable||console.error("Moveable modal requires draggable.js.");var e=this,i=e.options,o=e.$element.find(".modal-dialog").removeClass("modal-dragged");o.toggleClass("modal-moveable",!!i.moveable),e.$element.data("modal-moveable-setup")||o.draggable({container:e.$element,handle:".modal-header",before:function(){var t=o.css("margin-top");t&&"0px"!==t&&o.css("top",t).css("margin-top","").addClass("modal-dragged")},finish:function(o){var a=i.rememberPos;a&&(e.$element.data("modal-pos",o.pos),t.zui.store&&a!==!0&&t.zui.store.pageSet(n+".rememberPos."+a,o.pos))},move:"inside"!==i.moveable||function(t){a(o,t)}})},o.prototype.show=function(e,i){var a=this,r=t.Event("show."+n,{relatedTarget:e});a.$element.trigger(r),a.$element.toggleClass("modal-scroll-inside",!!a.options.scrollInside),a.isShown||r.isDefaultPrevented()||(a.isShown=!0,a.options.moveable&&a.setMoveable(),a.options.backdrop!==!1&&(a.setScrollbar(),a.$body.addClass("modal-open")),a.escape(),a.$element.on("click.dismiss."+n,'[data-dismiss="modal"]',function(t){a.hide(),t.stopPropagation()}),a.backdrop(function(){var r=t.support.transition&&a.$element.hasClass("fade");a.$element.parent().length||a.$element.appendTo(a.$body),a.$element.show().scrollTop(0),r&&a.$element[0].offsetWidth,a.$element.addClass("in").attr("aria-hidden",!1),a.adjustPosition(i),a.enforceFocus();var s=t.Event("shown."+n,{relatedTarget:e});r?a.$element.find(".modal-dialog").one("bsTransitionEnd",function(){a.$element.trigger("focus").trigger(s)}).emulateTransitionEnd(o.TRANSITION_DURATION):a.$element.trigger("focus").trigger(s)}))},o.prototype.hide=function(e){e&&e.preventDefault&&e.preventDefault();var i=this;e=t.Event("hide."+n),i.$element.trigger(e),i.isShown&&!e.isDefaultPrevented()&&(i.isShown=!1,i.options.backdrop!==!1&&(i.$body.removeClass("modal-open"),i.resetScrollbar()),i.escape(),t(document).off("focusin."+n),i.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss."+n),t.support.transition&&i.$element.hasClass("fade")?i.$element.one("bsTransitionEnd",i.hideModal.bind(i)).emulateTransitionEnd(o.TRANSITION_DURATION):i.hideModal())},o.prototype.enforceFocus=function(){t(document).off("focusin."+n).on("focusin."+n,function(t){this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")}.bind(this))},o.prototype.escape=function(){this.isShown&&this.options.keyboard?t(document).on("keydown.dismiss."+n,function(i){if(27==i.which){var o=t.Event("escaping."+n),a=this.$element.triggerHandler(o,"esc");if(a!=e&&!a)return;this.hide()}}.bind(this)):this.isShown||t(document).off("keydown.dismiss."+n)},o.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$element.trigger("hidden."+n)})},o.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},o.prototype.backdrop=function(e){var i=this,a=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=t.support.transition&&a;if(this.$backdrop=t('<div class="modal-backdrop '+a+'" />').appendTo(this.$body),this.$element.on("mousedown.dismiss."+n,function(t){t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))}.bind(this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;r?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var s=function(){i.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",s).emulateTransitionEnd(o.BACKDROP_TRANSITION_DURATION):s()}else e&&e()},o.prototype.setScrollbar=function(){t.zui.fixBodyScrollbar()&&this.options.onSetScrollbar&&this.options.onSetScrollbar()},o.prototype.resetScrollbar=function(){t.zui.resetBodyScrollbar(),this.options.onSetScrollbar&&this.options.onSetScrollbar("")},o.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var r=t.fn.modal;t.fn.modal=i,t.fn.modal.Constructor=o,t.fn.modal.noConflict=function(){return t.fn.modal=r,this},t(document).on("click."+n+".data-api",'[data-toggle="modal"]',function(e){var o=t(this),a=o.attr("href"),r=null;try{r=t(o.attr("data-target")||a&&a.replace(/.*(?=#[^\s]+$)/,""))}catch(s){return}if(r.length){var l=r.data(n)?"toggle":t.extend({remote:!/#/.test(a)&&a},r.data(),o.data());o.is("a")&&e.preventDefault(),r.one("show."+n,function(t){t.isDefaultPrevented()||r.one("hidden."+n,function(){o.is(":visible")&&o.trigger("focus")})}),i.call(r,l,this,o.data("position"))}})}(jQuery,void 0),function(t,e,i){"use strict";if(!t.fn.modal)throw new Error("Modal trigger requires modal.js");var n="zui.modaltrigger",o="ajax",a=".zui.modal",r="string",s=function(e,i){e=t.extend({},s.DEFAULTS,t.ModalTriggerDefaults,i?i.data():null,e),this.isShown,this.$trigger=i,this.options=e,this.id=t.zui.uuid(),e.show&&this.show()};s.DEFAULTS={type:"custom",height:"auto",name:"triggerModal",fade:!0,position:"fit",showHeader:!0,delay:0,backdrop:!0,keyboard:!0,waittime:0,loadingIcon:"icon-spinner-indicator",scrollInside:!1},s.prototype.initOptions=function(i){if(i.url&&(!i.type||i.type!=o&&"iframe"!=i.type)&&(i.type=o),i.remote)i.type=o,typeof i.remote===r&&(i.url=i.remote);else if(i.iframe)i.type="iframe",typeof i.iframe===r&&(i.url=i.iframe);else if(i.custom&&(i.type="custom",typeof i.custom===r)){var n;try{n=t(i.custom)}catch(a){}n&&n.length?i.custom=n:"function"==typeof e[i.custom]&&(i.custom=e[i.custom])}return i},s.prototype.init=function(e){var i=this,o=t("#"+e.name);o.length&&(i.isShown||o.off(a),o.remove()),o=t('<div id="'+e.name+'" class="modal modal-trigger '+(e.className||"")+'">'+("string"==typeof e.loadingIcon&&0===e.loadingIcon.indexOf("icon-")?'<div class="icon icon-spin loader '+e.loadingIcon+'"></div>':e.loadingIcon)+'<div class="modal-dialog"><div class="modal-content"><div class="modal-header"><button class="close" data-dismiss="modal">×</button><h4 class="modal-title"><i class="modal-icon"></i> <span class="modal-title-name"></span></h4></div><div class="modal-body"></div></div></div></div>').appendTo("body").data(n,i);var r=function(t,i,n){n=n||e[t],"function"==typeof n&&o.on(i+a,n)};r("onShow","show"),r("shown","shown"),r("onHide","hide",function(t){if("iframe"===e.type&&i.$iframeBody){var n=i.$iframeBody.triggerHandler("modalhide"+a,[i]);n===!1&&t.preventDefault()}var o=e.onHide;if(o)return o(t)}),r("hidden","hidden"),r("loaded","loaded"),o.on("shown"+a,function(){i.isShown=!0}).on("hidden"+a,function(){i.isShown=!1}),this.$modal=o,this.$dialog=o.find(".modal-dialog"),e.mergeOptions&&(this.options=e)},s.prototype.show=function(i){var a=this,l=t.extend({},s.DEFAULTS,a.options,{url:a.$trigger?a.$trigger.attr("href")||a.$trigger.attr("data-url")||a.$trigger.data("url"):a.options.url},i),d=a.isShown;l=a.initOptions(l),d||a.init(l);var h=a.$modal,c=h.find(".modal-dialog"),u=l.custom,p=c.find(".modal-body").css("padding","").toggleClass("load-indicator loading",!!d),f=c.find(".modal-header"),g=c.find(".modal-content");h.toggleClass("fade",l.fade).addClass(l.className).toggleClass("modal-loading",!d).toggleClass("modal-scroll-inside",!!l.scrollInside),c.toggleClass("modal-md","md"===l.size).toggleClass("modal-sm","sm"===l.size).toggleClass("modal-lg","lg"===l.size).toggleClass("modal-fullscreen","fullscreen"===l.size),f.toggle(l.showHeader),f.find(".modal-icon").attr("class","modal-icon icon-"+l.icon),f.find(".modal-title-name").text(l.title||""),l.size&&"fullscreen"===l.size&&(l.width="",l.height="");var m=function(){clearTimeout(this.resizeTask),this.resizeTask=setTimeout(function(){a.adjustPosition(l.position)},100)},v=function(t,e){return"undefined"==typeof t&&(t=l.delay),setTimeout(function(){c=h.find(".modal-dialog"),l.width&&"auto"!=l.width&&c.css("width",l.width),l.height&&"auto"!=l.height&&(c.css("height",l.height),"iframe"===l.type&&p.css("height",c.height()-f.outerHeight())),a.adjustPosition(l.position),h.removeClass("modal-loading").removeClass("modal-updating"),d&&p.removeClass("loading"),"iframe"!=l.type&&(p=c.off("resize."+n).find(".modal-body").off("resize."+n),l.scrollInside&&(p=p.children().off("resize."+n)),(p.length?p:c).on("resize."+n,m)),e&&e()},t)};if("custom"===l.type&&u)if("function"==typeof u){var y=u({modal:h,options:l,modalTrigger:a,ready:v});typeof y===r&&(p.html(y),v())}else u instanceof t?(p.html(t("<div>").append(u.clone()).html()),v()):(p.html(u),v());else if(l.url){var b=function(){var t=h.callComEvent(a,"broken");"string"==typeof t&&p.html(t),v()};if(h.attr("ref",l.url),"iframe"===l.type){h.addClass("modal-iframe"),this.firstLoad=!0;var w="iframe-"+l.name;f.detach(),p.detach(),g.empty().append(f).append(p),p.css("padding",0).html('<iframe id="'+w+'" name="'+w+'" src="'+l.url+'" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: 100%; left: 0px;"></iframe>'),l.waittime>0&&(a.waitTimeout=v(l.waittime,b));var C=document.getElementById(w);C.onload=C.onreadystatechange=function(i){var o=!!l.scrollInside;if(a.firstLoad&&h.addClass("modal-loading"),!this.readyState||"complete"==this.readyState){a.firstLoad=!1,l.waittime>0&&clearTimeout(a.waitTimeout);try{h.attr("ref",C.contentWindow.location.href);var r=e.frames[w].$;if(r&&"auto"===l.height&&"fullscreen"!=l.size){var s=r("body").addClass("body-modal").toggleClass("body-modal-scroll-inside",o);a.$iframeBody=s,l.iframeBodyClass&&s.addClass(l.iframeBodyClass);var d=[],c=function(i){h.removeClass("fade");var n=s.outerHeight();if(i===!0&&l.onlyIncreaseHeight&&(n=Math.max(n,p.data("minModalHeight")||0),p.data("minModalHeight",n)),o){var a=l.headerHeight;"number"!=typeof a?a=f.outerHeight():"function"==typeof a&&(a=a(f));var r=t(e).height();n=Math.min(n,r-a)}for(d.length>1&&n===d[0]&&(n=Math.max(n,d[1])),d.push(n);d.length>2;)d.shift();p.css("height",n),l.fade&&h.addClass("fade"),v()};h.callComEvent(a,"loaded",{modalType:"iframe",jQuery:r}),setTimeout(c,100),s.off("resize."+n).on("resize."+n,c),o&&t(e).off("resize."+n).on("resize."+n,c)}else v();var u=l.handleLinkInIframe;u&&r("body").on("click","string"==typeof u?u:"a[href]",function(){t(this).is('[data-toggle="modal"]')||h.addClass("modal-updating")}),l.iframeStyle&&r("head").append("<style>"+l.iframeStyle+"</style>")}catch(i){v()}}}}else t.ajax(t.extend({url:l.url,success:function(i){try{var r=t(i);r.filter(".modal-dialog").length?c.parent().empty().append(r):r.filter(".modal-content").length?c.find(".modal-content").replaceWith(r):p.wrapInner(r)}catch(s){e.console&&e.console.warn&&console.warn("ZUI: Cannot recogernize remote content.",{error:s,data:i}),h.html(i)}h.callComEvent(a,"loaded",{modalType:o}),v(),l.scrollInside&&t(e).off("resize."+n).on("resize."+n,m)},error:b},l.ajaxOptions))}d||h.modal({show:"show",backdrop:l.backdrop,moveable:l.moveable,rememberPos:l.rememberPos,keyboard:l.keyboard,scrollInside:l.scrollInside})},s.prototype.close=function(t,i){var n=this;(t||i)&&n.$modal.on("hidden"+a,function(){"function"==typeof t&&t(),typeof i===r&&i.length&&!n.$modal.data("cancel-reload")&&("this"===i?e.location.reload():e.location=i)}),n.$modal.modal("hide")},s.prototype.toggle=function(t){this.isShown?this.close():this.show(t)},s.prototype.adjustPosition=function(t){t=t===i?this.options.position:t,"function"==typeof t&&(t=t(this)),this.$modal.modal("adjustPosition",t)},t.zui({ModalTrigger:s,modalTrigger:new s}),t.fn.modalTrigger=function(e,i){return t(this).each(function(){var o=t(this),a=o.data(n),l=t.extend({title:o.attr("title")||o.text(),url:o.attr("href"),type:o.hasClass("iframe")?"iframe":""},o.data(),t.isPlainObject(e)&&e);return a?void(typeof e==r?a[e](i):l.show&&a.show(i)):(o.data(n,a=new s(l,o)),void o.on((l.trigger||"click")+".toggle."+n,function(e){l=t.extend(l,{url:o.attr("href")||o.attr("data-url")||o.data("url")||l.url}),a.toggle(l),o.is("a")&&e.preventDefault()}))})};var l=t.fn.modal;t.fn.modal=function(e,i){return t(this).each(function(){var n=t(this);n.hasClass("modal")?l.call(n,e,i):n.modalTrigger(e,i)})},t.fn.modal.bs=l;var d=function(e){return e?e=t(e):(e=t(".modal.modal-trigger"),!e.length),e&&e instanceof t?e:null},h=function(i,o,a){var r=i;if("function"==typeof i){var s=a;a=o,o=i,i=s}i=d(i),i&&i.length?i.each(function(){t(this).data(n).close(o,a)}):t("body").hasClass("modal-open")||t(".modal.in").length||t("body").hasClass("body-modal")&&e.parent.$.zui.closeModal(r,o,a)},c=function(t,e){e=d(e),e&&e.length&&e.modal("adjustPosition",t)},u=function(e,i){"string"==typeof e&&(e={url:e});var o=d(i);o&&o.length&&o.each(function(){t(this).data(n).show(e)})};t.zui({reloadModal:u,closeModal:h,ajustModalPosition:c,adjustModalPosition:c}),t(document).on("click."+n+".data-api",'[data-toggle="modal"]',function(e){var i=t(this),o=i.attr("href"),a=null;try{a=t(i.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,""))}catch(r){}a&&a.length||(i.data(n)?i.trigger(".toggle."+n):i.modalTrigger({show:!0})),i.is("a")&&e.preventDefault()}).on("click."+n+".data-api",'[data-dismiss="modal"]',function(){t.zui.closeModal()})}(window.jQuery,window,void 0),+function(t){"use strict";var e=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.init("tooltip",t,e)};e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1},e.prototype.init=function(e,i,n){this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n);for(var o=this.options.trigger.split(" "),a=o.length;a--;){var r=o[a];if("click"==r)this.$element.on("click."+this.type,this.options.selector,this.toggle.bind(this));else if("manual"!=r){var s="hover"==r?"mouseenter":"focus",l="hover"==r?"mouseleave":"blur";this.$element.on(s+"."+this.type,this.options.selector,this.enter.bind(this)),this.$element.on(l+"."+this.type,this.options.selector,this.leave.bind(this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return e=t.extend({},this.getDefaults(),this.$element.data(),e),e.delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},e.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show()},e.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type);return clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide()},e.prototype.show=function(e){var i=t.Event("show.zui."+this.type);if((e||this.hasContent())&&this.enabled){var n=this;if(n.$element.trigger(i),i.isDefaultPrevented())return;var o=n.tip();n.setContent(e),n.options.animation&&o.addClass("fade");var a="function"==typeof n.options.placement?n.options.placement.call(n,o[0],n.$element[0]):n.options.placement,r=/\s?auto?\s?/i,s=r.test(a);s&&(a=a.replace(r,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(a),n.options.container?o.appendTo(n.options.container):o.insertAfter(n.$element);var l=n.getPosition(),d=o[0].offsetWidth,h=o[0].offsetHeight;if(s){var c=n.$element.parent(),u=a,p=document.documentElement.scrollTop||document.body.scrollTop,f="body"==n.options.container?window.innerWidth:c.outerWidth(),g="body"==n.options.container?window.innerHeight:c.outerHeight(),m="body"==n.options.container?0:c.offset().left;a="bottom"==a&&l.top+l.height+h-p>g?"top":"top"==a&&l.top-p-h<0?"bottom":"right"==a&&l.right+d>f?"left":"left"==a&&l.left-d<m?"right":a,o.removeClass(u).addClass(a)}var v=n.getCalculatedOffset(a,l,d,h);n.applyPlacement(v,a);var y=function(){var t=n.hoverState;n.$element.trigger("shown.zui."+n.type),n.hoverState=null,"out"==t&&n.leave(n)};t.support.transition&&n.$tip.hasClass("fade")?o.one("bsTransitionEnd",y).emulateTransitionEnd(150):y()}},e.prototype.applyPlacement=function(t,e){var i,n=this.tip(),o=n[0].offsetWidth,a=n[0].offsetHeight,r=parseInt(n.css("margin-top"),10),s=parseInt(n.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(s)&&(s=0),t.top=t.top+r,t.left=t.left+s,n.offset(t).addClass("in");var l=n[0].offsetWidth,d=n[0].offsetHeight;if("top"==e&&d!=a&&(i=!0,t.top=t.top+a-d),/bottom|top/.test(e)){var h=0;t.left<0&&(h=t.left*-2,t.left=0,n.offset(t),l=n[0].offsetWidth,d=n[0].offsetHeight),this.replaceArrow(h-o+l,l,"left")}else this.replaceArrow(d-a,d,"top");i&&n.offset(t)},e.prototype.replaceArrow=function(t,e,i){this.arrow().css(i,t?50*(1-t/e)+"%":"")},e.prototype.setContent=function(t){var e=this.tip(),i=t||this.getTitle();this.options.tipId&&e.attr("id",this.options.tipId),this.options.tipClass&&e.addClass(this.options.tipClass),e.find(".tooltip-inner")[this.options.html?"html":"text"](i),e.removeClass("fade in top bottom left right")},e.prototype.hide=function(){function e(){"in"!=i.hoverState&&n.detach()}var i=this,n=this.tip(),o=t.Event("hide.zui."+this.type);if(this.$element.trigger(o),!o.isDefaultPrevented())return n.removeClass("in"),t.support.transition&&this.$tip.hasClass("fade")?n.one(t.support.transition.end,e).emulateTransitionEnd(150):e(),this.$element.trigger("hidden.zui."+this.type),this},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(){var e=this.$element[0];return t.extend({},"function"==typeof e.getBoundingClientRect?e.getBoundingClientRect():{width:e.offsetWidth,height:e.offsetHeight},this.$element.offset())},e.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},e.prototype.getTitle=function(){var t,e=this.$element,i=this.options;return t=e.attr("data-original-title")||("function"==typeof i.title?i.title.call(e[0]):i.title)},e.prototype.tip=function(){return this.$tip=this.$tip||t(this.options.template)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var i=e?t(e.currentTarget)[this.type](this.getDelegateOptions()).data("zui."+this.type):this;i.tip().hasClass("in")?i.leave(i):i.enter(i)},e.prototype.destroy=function(){this.hide().$element.off("."+this.type).removeData("zui."+this.type)};var i=t.fn.tooltip;t.fn.tooltip=function(i,n){return this.each(function(){var o=t(this),a=o.data("zui.tooltip"),r="object"==typeof i&&i;a||o.data("zui.tooltip",a=new e(this,r)),"string"==typeof i&&a[i](n)})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=i,this}}(window.jQuery),+function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTarget();if(e)return e.find(".arrow").length<1&&t.addClass("no-arrow"),void t.html(e.html());var i=this.getTitle(),n=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](i),t.find(".popover-content")[this.options.html?"html":"text"](n),t.removeClass("fade top bottom left right in"),this.options.tipId&&t.attr("id",this.options.tipId),this.options.tipClass&&t.addClass(this.options.tipClass),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTarget()||this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.getTarget=function(){var e=this.$element,i=this.options,n=e.attr("data-target")||("function"==typeof i.target?i.target.call(e[0]):i.target);return!!n&&("$next"==n?e.next(".popover"):t(n))},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")},e.prototype.tip=function(){return this.$tip||(this.$tip=t(this.options.template)),this.$tip};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var n=t(this),o=n.data("zui.popover"),a="object"==typeof i&&i;o||n.data("zui.popover",o=new e(this,a)),"string"==typeof i&&o[i]()})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(window.jQuery),+function(t){"use strict";function e(e){t(o).remove(),t(a).each(function(e){var o=i(t(this));o.hasClass("open")&&(o.trigger(e=t.Event("hide."+n)),e.isDefaultPrevented()||o.removeClass("open").trigger("hidden."+n))})}function i(e){var i=e.attr("data-target");i||(i=e.attr("href"),i=i&&/#/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n;try{n=i&&t(i)}catch(o){}return n&&n.length?n:e.parent()}var n="zui.dropdown",o=".dropdown-backdrop",a="[data-toggle=dropdown]",r=function(e){t(e).on("click."+n,this.toggle)};r.prototype.toggle=function(o){var a=t(this);if(!a.is(".disabled, :disabled")){var r=i(a),s=r.hasClass("open");if(e(),!s){if("ontouchstart"in document.documentElement&&!r.closest(".navbar-nav").length&&t('<div class="dropdown-backdrop"/>').insertAfter(t(this)).on("click",e),r.trigger(o=t.Event("show."+n)),o.isDefaultPrevented())return;r.toggleClass("open").trigger("shown."+n),a.focus()}return!1}},r.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var n=t(this);if(e.preventDefault(),e.stopPropagation(),!n.is(".disabled, :disabled")){var o=i(n),r=o.hasClass("open");if(!r||r&&27==e.keyCode)return 27==e.which&&o.find(a).focus(),n.click();var s=t("[role=menu] li:not(.divider):visible a",o);if(s.length){var l=s.index(s.filter(":focus"));38==e.keyCode&&l>0&&l--,40==e.keyCode&&l<s.length-1&&l++,~l||(l=0),s.eq(l).focus()}}}};var s=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),n=i.data("dropdown");n||i.data("dropdown",n=new r(this)),"string"==typeof e&&n[e].call(i)})},t.fn.dropdown.Constructor=r,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=s,this};var l=n+".data-api";t(document).on("click."+l,e).on("click."+l,".dropdown form,.not-clear-menu",function(t){t.stopPropagation()}).on("click."+l,a,r.prototype.toggle).on("keydown."+l,a+", [role=menu]",r.prototype.keydown)}(window.jQuery),+function(t){"use strict";var e=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter",this.pause.bind(this)).on("mouseleave",this.cycle.bind(this))};e.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,touchable:!0},e.prototype.touchable=function(){function e(e){var e=e||window.event;e.originalEvent&&(e=e.originalEvent);var a=t(this);switch(e.type){case"touchstart":n=e.touches[0].pageX,o=e.touches[0].pageY;break;case"touchend":var r=e.changedTouches[0].pageX-n,s=e.changedTouches[0].pageY-o;if(Math.abs(r)>Math.abs(s))i(a,r),Math.abs(r)>10&&e.preventDefault();else{var l=t(window);t("body,html").animate({scrollTop:l.scrollTop()-s},400)}}}function i(t,e){e>10?a.prev():e<-10&&a.next()}if(this.options.touchable){this.$element.on("touchstart touchmove touchend",e);var n,o,a=this}},e.prototype.cycle=function(t){return t||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(this.next.bind(this),this.options.interval)),this},e.prototype.getActiveIndex=function(){return this.$active=this.$element.find(".item.active"),this.$items=this.$active.parent().children(),
this.$items.index(this.$active)},e.prototype.to=function(e){var i=this,n=this.getActiveIndex();if(!(e>this.$items.length-1||e<0))return this.sliding?this.$element.one("slid",function(){i.to(e)}):n==e?this.pause().cycle():this.slide(e>n?"next":"prev",t(this.$items[e]))},e.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition.end&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},e.prototype.next=function(){if(!this.sliding)return this.slide("next")},e.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},e.prototype.slide=function(e,i){var n=this.$element.find(".item.active"),o=i||n[e](),a=this.interval,r="next"==e?"left":"right",s="next"==e?"first":"last",l=this;if(!o.length){if(!this.options.wrap)return;o=this.$element.find(".item")[s]()}this.sliding=!0,a&&this.pause();var d=t.Event("slide.zui.carousel",{relatedTarget:o[0],direction:r});if(!o.hasClass("active")){if(this.$indicators.length&&(this.$indicators.find(".active").removeClass("active"),this.$element.one("slid",function(){var e=t(l.$indicators.children()[l.getActiveIndex()]);e&&e.addClass("active")})),t.support.transition&&this.$element.hasClass("slide")){if(this.$element.trigger(d),d.isDefaultPrevented())return;o.addClass(e),o[0].offsetWidth,n.addClass(r),o.addClass(r),n.one(t.support.transition.end,function(){o.removeClass([e,r].join(" ")).addClass("active"),n.removeClass(["active",r].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger("slid")},0)}).emulateTransitionEnd(600)}else{if(this.$element.trigger(d),d.isDefaultPrevented())return;n.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger("slid")}return a&&this.cycle(),this}};var i=t.fn.carousel;t.fn.carousel=function(i){return this.each(function(){var n=t(this),o=n.data("zui.carousel"),a=t.extend({},e.DEFAULTS,n.data(),"object"==typeof i&&i),r="string"==typeof i?i:a.slide;o||n.data("zui.carousel",o=new e(this,a)),"number"==typeof i?o.to(i):r?o[r]():a.interval&&o.pause().cycle(),a.touchable&&o.touchable()})},t.fn.carousel.Constructor=e,t.fn.carousel.noConflict=function(){return t.fn.carousel=i,this},t(document).on("click.zui.carousel.data-api","[data-slide], [data-slide-to]",function(e){var i,n=t(this),o=t(n.attr("data-target")||(i=n.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"")),a=t.extend({},o.data(),n.data()),r=n.attr("data-slide-to");r&&(a.interval=!1),o.carousel(a),(r=n.attr("data-slide-to"))&&o.data("zui.carousel").to(r),e.preventDefault()}),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var e=t(this);e.carousel(e.data())})})}(window.jQuery);