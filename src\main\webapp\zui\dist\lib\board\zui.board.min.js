/*!
 * ZUI: 看板 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t){"use strict";if(!t.fn.droppable)throw new Error("Droppable requires for boards");var a="zui.boards",o=function(a,o){this.$=t(a),this.options=this.getOptions(o),this.getLang(),this.init()};o.DEFAULTS={lang:"en",langs:{zh_cn:{append2end:"移动到末尾"},zh_tw:{append2end:"移动到末尾"},en:{append2end:"Move to the end."}}},o.prototype.getOptions=function(a){return a=t.extend({},o.DEFAULTS,this.$.data(),a)},o.prototype.getLang=function(){var o=this.options;this.langName=o.lang||t.zui.clientLang(),this.lang=t.zui.getLangData(a,this.langName,o.langs)},o.prototype.init=function(){var a=1,o=this.lang;this.$.find('.board-item:not(".disable-drop"), .board:not(".disable-drop")').each(function(){var i=t(this);i.attr("id")?i.attr("data-id",i.attr("id")):i.attr("data-id")||i.attr("data-id","board"+a++),i.hasClass("board")&&i.find(".board-list").append('<div class="board-item board-item-empty"><i class="icon-plus"></i> {append2end}</div>'.format(o)).append('<div class="board-item board-item-shadow"></div>'.format(o))}),this.bind()},o.prototype.bind=function(a){var o=this.$,i=this.options;o.droppable(t.extend({before:i.before,target:'.board-item:not(".disable-drop, .board-item-shadow")',flex:!0,selector:'.board-item:not(".disable-drop, .board-item-shadow")',start:function(t){o.addClass("dragging").find(".board-item-shadow").height(t.element.outerHeight())},drag:function(t){if(o.find(".board.drop-in-empty").removeClass("drop-in-empty"),t.isIn){var a=t.target.closest(".board").addClass("drop-in"),i=a.find(".board-item-shadow"),e=t.target;o.addClass("drop-in").find(".board.drop-in").not(a).removeClass("drop-in"),i.insertBefore(e),a.toggleClass("drop-in-empty",e.hasClass("board-item-empty"))}},drop:function(t){if(t.isNew){var a;"function"==typeof i.drop&&(a=i.drop(t)),a!==!1&&t.element.insertBefore(t.target)}},finish:function(){o.removeClass("dragging").removeClass("drop-in").find(".board.drop-in").removeClass("drop-in")}},i.droppable))},t.fn.boards=function(i){return this.each(function(){var e=t(this),n=e.data(a),r="object"==typeof i&&i;n||e.data(a,n=new o(this,r)),"string"==typeof i&&n[i]()})},t.fn.boards.Constructor=o}(jQuery);