/*!
<PERSON><PERSON>, a Select Box Enhancer for j<PERSON><PERSON>y and Prototype
by <PERSON> for Harvest, http://getharvest.com

Version 1.1.0
Full source at https://github.com/harvesthq/chosen
Copyright (c) 2011 Harvest http://getharvest.com

MIT License, https://github.com/harvesthq/chosen/blob/master/LICENSE.md
*/
(function(){var t,e,s,i,r,o={}.hasOwnProperty,n=function(t,e){function s(){this.constructor=t}for(var i in e)o.call(e,i)&&(t[i]=e[i]);return s.prototype=e.prototype,t.prototype=new s,t.__super__=e.prototype,t},h={zh_cn:{no_results_text:"没有找到"},zh_tw:{no_results_text:"沒有找到"},en:{no_results_text:"No results match"}},l={};i=function(){function e(){this.options_index=0,this.parsed=[]}return e.prototype.add_node=function(t){return"OPTGROUP"===t.nodeName.toUpperCase()?this.add_group(t):this.add_option(t)},e.prototype.add_group=function(e){var s,i,r,o,n,h;for(s=this.parsed.length,this.parsed.push({array_index:s,group:!0,label:this.escapeExpression(e.label),children:0,disabled:e.disabled,title:e.title,search_keys:t.trim(e.getAttribute("data-keys")||"").replace(/,/g," ")}),n=e.childNodes,h=[],r=0,o=n.length;r<o;r++)i=n[r],h.push(this.add_option(i,s,e.disabled));return h},e.prototype.add_option=function(e,s,i){if("OPTION"===e.nodeName.toUpperCase())return""!==e.text?(null!=s&&(this.parsed[s].children+=1),this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,value:e.value,text:e.text,title:e.title,html:e.innerHTML,selected:e.selected,disabled:i===!0?i:e.disabled,group_array_index:s,classes:e.className,style:e.style.cssText,data:e.getAttribute("data-data"),search_keys:(t.trim(e.getAttribute("data-keys")||"")+e.value).replace(/,/," ")})):this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,empty:!0}),this.options_index+=1},e.prototype.escapeExpression=function(t){var e,s;return null==t||t===!1?"":/[\&\<\>\"\'\`]/.test(t)?(e={"<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},s=/&(?!\w+;)|[\<\>\"\'\`]/g,t.replace(s,function(t){return e[t]||"&amp;"})):t},e}(),i.select_to_array=function(t){var e,s,r,o,n;for(s=new i,n=t.childNodes,r=0,o=n.length;r<o;r++)e=n[r],s.add_node(e);return s.parsed},e=function(){function e(s,i){if(this.form_field=s,this.options=t.extend({},l,null!=i?i:{}),e.browser_is_supported()){var r=this.options.lang||t.zui.clientLang?t.zui.clientLang():"en",o=t.zui.clientLang?t.zui.clientLang():"en";t.isPlainObject(r)?this.lang=t.zui.getLangData?t.zui.getLangData("chosen",o,h):t.extend(r,h.en,h[o]):this.lang=t.zui.getLangData?t.zui.getLangData("chosen",r,h):h[r||o]||h.en,this.is_multiple=this.form_field.multiple,this.set_default_text(),this.set_default_values(),this.setup(),this.set_up_html(),this.register_observers()}}return e.prototype.set_default_values=function(){var t=this,e=t.options;t.click_test_action=function(e){return t.test_active_click(e)},t.activate_action=function(e){return t.activate_field(e)},t.active_field=!1,t.mouse_on_container=!1,t.results_showing=!1,t.result_highlighted=null,t.allow_single_deselect=null!=e.allow_single_deselect&&null!=this.form_field.options[0]&&""===t.form_field.options[0].text&&e.allow_single_deselect,t.disable_search_threshold=e.disable_search_threshold||0,t.disable_search=e.disable_search||!1,t.enable_split_word_search=null==e.enable_split_word_search||e.enable_split_word_search,t.group_search=null==e.group_search||e.group_search,t.search_contains=e.search_contains||!1,t.single_backstroke_delete=null==e.single_backstroke_delete||e.single_backstroke_delete,t.max_selected_options=e.max_selected_options||1/0,t.drop_direction=e.drop_direction||"auto",t.drop_item_height=void 0!==e.drop_item_height?e.drop_item_height:25,t.max_drop_height=void 0!==e.max_drop_height?e.max_drop_height:240,t.middle_highlight=e.middle_highlight,t.compact_search=e.compact_search||!1,t.inherit_select_classes=e.inherit_select_classes||!1,t.display_selected_options=null==e.display_selected_options||e.display_selected_options,t.sort_value_splitter=e.sort_value_spliter||e.sort_value_splitter||",",t.sort_field=e.sort_field;var s=e.max_drop_width;return"string"==typeof s&&s.indexOf("px")===s.length-2&&(s=parseInt(s.substring(0,s.length-2))),t.max_drop_width=s,t.display_disabled_options=null==e.display_disabled_options||e.display_disabled_options},e.prototype.set_default_text=function(){return this.form_field.getAttribute("data-placeholder")?this.default_text=this.form_field.getAttribute("data-placeholder"):this.is_multiple?this.default_text=this.options.placeholder_text_multiple||this.options.placeholder_text||e.default_multiple_text:this.default_text=this.options.placeholder_text_single||this.options.placeholder_text||e.default_single_text,this.results_none_found=this.form_field.getAttribute("data-no_results_text")||this.options.no_results_text||this.lang.no_results_text||e.default_no_result_text},e.prototype.mouse_enter=function(){return this.mouse_on_container=!0},e.prototype.mouse_leave=function(){return this.mouse_on_container=!1},e.prototype.input_focus=function(t){var e=this;if(this.is_multiple){if(!this.active_field)return setTimeout(function(){return e.container_mousedown()},50)}else if(!this.active_field)return this.activate_field()},e.prototype.input_blur=function(t){var e=this;if(!this.mouse_on_container)return this.active_field=!1,setTimeout(function(){return e.blur_test()},100)},e.prototype.results_option_build=function(e){var s,i,r,o,n;s="",n=this.results_data;var h=e&&e.first?[]:null;for(r=0,o=n.length;r<o;r++)i=n[r],s+=i.group?this.result_add_group(i):this.result_add_option(i),h&&i.selected&&h.push(i);if(h){var l,a;if(this.sort_field&&this.is_multiple){l=t(this.sort_field);var c=l.val();if(a="string"==typeof c&&c.length?c.split(this.sort_value_splitter):[],a.length){var _={};for(r=0;r<a.length;++r)_[a[r]]=r;h.sort(function(t,e){var s=_[t.value],i=_[e.value];return void 0===s&&(s=0),void 0===i&&(i=0),s-i})}}for(a=[],r=0;r<h.length;++r)i=h[r],this.is_multiple?(this.choice_build(i),a.push(i.value)):this.single_set_selected_text(i.text);l&&l.length&&l.val(a.join(this.sort_value_splitter))}return s},e.prototype.result_add_option=function(t){var e,s;return t.search_match&&this.include_option_in_results(t)?(e=[],t.disabled||t.selected&&this.is_multiple||e.push("active-result"),!t.disabled||t.selected&&this.is_multiple||e.push("disabled-result"),t.selected&&e.push("result-selected"),null!=t.group_array_index&&e.push("group-option"),""!==t.classes&&e.push(t.classes),s=document.createElement("li"),s.className=e.join(" "),s.style.cssText=t.style,s.title=t.title,s.setAttribute("data-option-array-index",t.array_index),s.setAttribute("data-data",t.data),s.innerHTML=t.search_text,this.outerHTML(s)):""},e.prototype.result_add_group=function(t){var e;return(t.search_match||t.group_match)&&t.active_options>0?(e=document.createElement("li"),e.className="group-result",e.title=t.title,e.innerHTML=t.search_text,this.outerHTML(e)):""},e.prototype.results_update_field=function(){this.set_default_text(),this.is_multiple||this.results_reset_cleanup(),this.result_clear_highlight(),this.results_build(),this.results_showing&&(this.winnow_results(),this.autoResizeDrop())},e.prototype.reset_single_select_options=function(){var t,e,s,i,r;for(i=this.results_data,r=[],e=0,s=i.length;e<s;e++)t=i[e],t.selected?r.push(t.selected=!1):r.push(void 0);return r},e.prototype.results_toggle=function(){return this.results_showing?this.results_hide():this.results_show()},e.prototype.results_search=function(t){return this.results_showing?this.winnow_results(1):this.results_show()},e.prototype.winnow_results=function(t){var e,s,i,r,o,n,h,l,a,c,_,u,d;for(this.no_results_clear(),o=0,h=this.get_search_text(),e=h.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),r=this.search_contains?"":"^",i=new RegExp(r+e,"i"),c=new RegExp(e,"i"),d=this.results_data,_=0,u=d.length;_<u;_++)s=d[_],s.search_match=!1,n=null,this.include_option_in_results(s)&&(s.group&&(s.group_match=!1,s.active_options=0),null!=s.group_array_index&&this.results_data[s.group_array_index]&&(n=this.results_data[s.group_array_index],0===n.active_options&&n.search_match&&(o+=1),n.active_options+=1),s.group&&!this.group_search||(s.search_text=s.group?s.label:s.html,s.search_keys_match=this.search_string_match(s.search_keys,i),s.search_text_match=this.search_string_match(s.search_text,i),s.search_match=s.search_text_match||s.search_keys_match,s.search_match&&!s.group&&(o+=1),s.search_match?(s.search_text_match&&s.search_text.length?(l=s.search_text.search(c),a=s.search_text.substr(0,l+h.length)+"</em>"+s.search_text.substr(l+h.length),s.search_text=a.substr(0,l)+"<em>"+a.substr(l)):s.search_keys_match&&s.search_keys.length&&(l=s.search_keys.search(c),a=s.search_keys.substr(0,l+h.length)+"</em>"+s.search_keys.substr(l+h.length),s.search_text+='&nbsp; <small style="opacity: 0.7">'+a.substr(0,l)+"<em>"+a.substr(l)+"</small>"),null!=n&&(n.group_match=!0)):null!=s.group_array_index&&this.results_data[s.group_array_index].search_match&&(s.search_match=!0)));return this.result_clear_highlight(),o<1&&h.length?(this.update_results_content(""),this.no_results(h)):(this.update_results_content(this.results_option_build()),this.winnow_results_set_highlight(t))},e.prototype.search_string_match=function(t,e){var s,i,r,o;if(e.test(t))return!0;if(this.enable_split_word_search&&(t.indexOf(" ")>=0||0===t.indexOf("["))&&(i=t.replace(/\[|\]/g,"").split(" "),i.length))for(r=0,o=i.length;r<o;r++)if(s=i[r],e.test(s))return!0},e.prototype.choices_count=function(){var t,e,s,i;if(null!=this.selected_option_count)return this.selected_option_count;for(this.selected_option_count=0,i=this.form_field.options,e=0,s=i.length;e<s;e++)t=i[e],t.selected&&""!=t.value&&(this.selected_option_count+=1);return this.selected_option_count},e.prototype.choices_click=function(t){if(t.preventDefault(),!this.results_showing&&!this.is_disabled)return this.results_show()},e.prototype.keyup_checker=function(t){var e,s;switch(e=null!=(s=t.which)?s:t.keyCode,this.search_field_scale(),e){case 8:if(this.is_multiple&&this.backstroke_length<1&&this.choices_count()>0)return this.keydown_backstroke();if(!this.pending_backstroke)return this.result_clear_highlight(),this.results_search();break;case 13:if(t.preventDefault(),this.results_showing)return this.result_select(t);break;case 27:return this.results_showing&&this.results_hide(),!0;case 9:case 38:case 40:case 16:case 91:case 17:break;default:return this.results_search()}},e.prototype.clipboard_event_checker=function(t){var e=this;return setTimeout(function(){return e.results_search()},50)},e.prototype.container_width=function(){return null!=this.options.width?this.options.width:this.form_field&&this.form_field.classList&&this.form_field.classList.contains("form-control")?"100%":""+this.form_field.offsetWidth+"px"},e.prototype.include_option_in_results=function(t){return!(this.is_multiple&&!this.display_selected_options&&t.selected)&&(!(!this.display_disabled_options&&t.disabled)&&!t.empty)},e.prototype.search_results_touchstart=function(t){return this.touch_started=!0,this.search_results_mouseover(t)},e.prototype.search_results_touchmove=function(t){return this.touch_started=!1,this.search_results_mouseout(t)},e.prototype.search_results_touchend=function(t){if(this.touch_started)return this.search_results_mouseup(t)},e.prototype.outerHTML=function(t){var e;return t.outerHTML?t.outerHTML:(e=document.createElement("div"),e.appendChild(t),e.innerHTML)},e.browser_is_supported=function(){return"Microsoft Internet Explorer"===window.navigator.appName?document.documentMode>=8:!/iP(od|hone)/i.test(window.navigator.userAgent)&&(!/Android/i.test(window.navigator.userAgent)||!/Mobile/i.test(window.navigator.userAgent))},e.default_multiple_text="",e.default_single_text="",e.default_no_result_text="No results match",e}(),t=jQuery,t.fn.extend({chosen:function(i){return e.browser_is_supported()?this.each(function(e){var r=t(this),o=r.data("chosen");"destroy"===i&&o?o.destroy():o||r.data("chosen",new s(this,t.extend({},r.data(),i)))}):this}}),s=function(e){function s(){return r=s.__super__.constructor.apply(this,arguments)}return n(s,e),s.prototype.setup=function(){return this.form_field_jq=t(this.form_field),this.current_selectedIndex=this.form_field.selectedIndex,this.is_rtl=this.form_field_jq.hasClass("chosen-rtl")},s.prototype.set_up_html=function(){var e,s;e=["chosen-container"],e.push("chosen-container-"+(this.is_multiple?"multi":"single")),this.inherit_select_classes&&this.form_field.className&&e.push(this.form_field.className),this.is_rtl&&e.push("chosen-rtl");var i=this.form_field.getAttribute("data-css-class");return i&&e.push(i),s={"class":e.join(" "),style:"width: "+this.container_width()+";",title:this.form_field.title},this.form_field.id.length&&(s.id=this.form_field.id.replace(/[^\w]/g,"_")+"_chosen"),this.container=t("<div />",s),this.is_multiple?this.container.html('<ul class="chosen-choices"><li class="search-field"><input type="text" value="'+this.default_text+'" class="default" autocomplete="off" style="width:25px;" /></li></ul><div class="chosen-drop"><ul class="chosen-results"></ul></div>'):(this.container.html('<a class="chosen-single chosen-default" tabindex="-1"><span>'+this.default_text+'</span><div><b></b></div><div class="chosen-search"><input type="text" autocomplete="off" /></div></a><div class="chosen-drop"><ul class="chosen-results"></ul></div>'),this.compact_search?this.container.addClass("chosen-compact").find(".chosen-search").appendTo(this.container.find(".chosen-single")):this.container.find(".chosen-search").prependTo(this.container.find(".chosen-drop")),this.options.highlight_selected!==!1&&this.container.addClass("chosen-highlight-selected")),this.form_field_jq.hide().after(this.container),this.dropdown=this.container.find("div.chosen-drop").first(),this.search_field=this.container.find("input").first(),this.search_results=this.container.find("ul.chosen-results").first(),this.search_field_scale(),this.search_no_results=this.container.find("li.no-results").first(),this.is_multiple?(this.search_choices=this.container.find("ul.chosen-choices").first(),this.search_container=this.container.find("li.search-field").first()):(this.search_container=this.container.find("div.chosen-search").first(),this.selected_item=this.container.find(".chosen-single").first()),this.options.drop_width&&this.dropdown.css("width",this.options.drop_width).addClass("chosen-drop-size-limited"),this.max_drop_width&&this.dropdown.addClass("chosen-auto-max-width"),this.options.no_wrap&&this.dropdown.addClass("chosen-no-wrap"),this.results_build(),this.set_tab_index(),this.set_label_behavior(),this.form_field_jq.trigger("chosen:ready",{chosen:this})},s.prototype.register_observers=function(){var t=this;return this.container.bind("mousedown.chosen",function(e){t.container_mousedown(e)}),this.container.bind("mouseup.chosen",function(e){t.container_mouseup(e)}),this.container.bind("mouseenter.chosen",function(e){t.mouse_enter(e)}),this.container.bind("mouseleave.chosen",function(e){t.mouse_leave(e)}),this.search_results.bind("mouseup.chosen",function(e){t.search_results_mouseup(e)}),this.search_results.bind("mouseover.chosen",function(e){t.search_results_mouseover(e)}),this.search_results.bind("mouseout.chosen",function(e){t.search_results_mouseout(e)}),this.search_results.bind("mousewheel.chosen DOMMouseScroll.chosen",function(e){t.search_results_mousewheel(e)}),this.search_results.bind("touchstart.chosen",function(e){t.search_results_touchstart(e)}),this.search_results.bind("touchmove.chosen",function(e){t.search_results_touchmove(e)}),this.search_results.bind("touchend.chosen",function(e){t.search_results_touchend(e)}),this.form_field_jq.bind("chosen:updated.chosen",function(e){t.results_update_field(e)}),this.form_field_jq.bind("chosen:activate.chosen",function(e){t.activate_field(e)}),this.form_field_jq.bind("chosen:open.chosen",function(e){t.container_mousedown(e)}),this.form_field_jq.bind("chosen:close.chosen",function(e){t.input_blur(e)}),this.search_field.bind("blur.chosen",function(e){t.input_blur(e)}),this.search_field.bind("keyup.chosen",function(e){t.keyup_checker(e)}),this.search_field.bind("keydown.chosen",function(e){t.keydown_checker(e)}),this.search_field.bind("focus.chosen",function(e){t.input_focus(e)}),this.search_field.bind("cut.chosen",function(e){t.clipboard_event_checker(e)}),this.search_field.bind("paste.chosen",function(e){t.clipboard_event_checker(e)}),this.is_multiple?this.search_choices.bind("click.chosen",function(e){t.choices_click(e)}):this.container.bind("click.chosen",function(t){t.preventDefault()})},s.prototype.destroy=function(){return t(this.container[0].ownerDocument).unbind("click.chosen",this.click_test_action),this.search_field[0].tabIndex&&(this.form_field_jq[0].tabIndex=this.search_field[0].tabIndex),this.container.remove(),this.form_field_jq.removeData("chosen"),this.form_field_jq.show()},s.prototype.search_field_disabled=function(){return this.is_disabled=this.form_field_jq[0].disabled,this.is_disabled?(this.container.addClass("chosen-disabled"),this.search_field[0].disabled=!0,this.is_multiple||this.selected_item.unbind("focus.chosen",this.activate_action),this.close_field()):(this.container.removeClass("chosen-disabled"),this.search_field[0].disabled=!1,this.is_multiple?void 0:this.selected_item.bind("focus.chosen",this.activate_action))},s.prototype.container_mousedown=function(e){if(!this.is_disabled&&(e&&"mousedown"===e.type&&!this.results_showing&&e.preventDefault(),null==e||!t(e.target).hasClass("search-choice-close")))return this.active_field?this.is_multiple||!e||t(e.target)[0]!==this.selected_item[0]&&!t(e.target).parents("a.chosen-single").length||(e.preventDefault(),this.results_toggle()):(this.is_multiple&&this.search_field.val(""),t(this.container[0].ownerDocument).bind("click.chosen",this.click_test_action),this.results_show()),this.activate_field()},s.prototype.container_mouseup=function(t){if("ABBR"===t.target.nodeName&&!this.is_disabled)return this.results_reset(t)},s.prototype.search_results_mousewheel=function(t){var e;if(t.originalEvent&&(e=-t.originalEvent.wheelDelta||t.originalEvent.detail),null!=e)return t.preventDefault(),"DOMMouseScroll"===t.type&&(e=40*e),this.search_results.scrollTop(e+this.search_results.scrollTop())},s.prototype.blur_test=function(t){if(!this.active_field&&this.container.hasClass("chosen-container-active"))return this.close_field()},s.prototype.close_field=function(){return t(this.container[0].ownerDocument).unbind("click.chosen",this.click_test_action),this.active_field=!1,this.results_hide(),this.container.removeClass("chosen-container-active"),this.clear_backstroke(),this.show_search_field_default(),this.search_field_scale()},s.prototype.activate_field=function(){return this.container.addClass("chosen-container-active"),this.active_field=!0,this.search_field.val(this.search_field.val()),this.search_field.focus()},s.prototype.test_active_click=function(e){var s;return s=t(e.target).closest(".chosen-container"),s.length&&this.container[0]===s[0]?this.active_field=!0:this.close_field()},s.prototype.results_build=function(){return this.parsing=!0,this.selected_option_count=null,this.results_data=i.select_to_array(this.form_field),this.is_multiple?this.search_choices.find("li.search-choice").remove():this.is_multiple||(this.single_set_selected_text(),this.disable_search||this.form_field.options.length<=this.disable_search_threshold?(this.search_field[0].readOnly=!0,this.container.addClass("chosen-container-single-nosearch"),this.container.removeClass("chosen-with-search")):(this.search_field[0].readOnly=!1,this.container.removeClass("chosen-container-single-nosearch"),this.container.addClass("chosen-with-search"))),this.update_results_content(this.results_option_build({first:!0})),this.search_field_disabled(),this.show_search_field_default(),this.search_field_scale(),this.parsing=!1},s.prototype.result_do_highlight=function(t,e){if(t.length){var s,i,r,o,n,h,l=-1;this.result_clear_highlight(),this.result_highlight=t,this.result_highlight.addClass("highlighted"),r=parseInt(this.search_results.css("maxHeight"),10),h=this.result_highlight.outerHeight(),n=this.search_results.scrollTop(),o=r+n,i=this.result_highlight.position().top+this.search_results.scrollTop(),s=i+h,this.middle_highlight&&(e||"always"===this.middle_highlight)?l=Math.min(i-h,Math.max(0,i-(r-h)/2)):s>=o?l=s-r>0?s-r:0:i<n&&(l=i),l>-1?this.search_results.scrollTop(l):this.result_highlight.scrollIntoView&&this.result_highlight.scrollIntoView()}},s.prototype.result_clear_highlight=function(){return this.result_highlight&&this.result_highlight.removeClass("highlighted"),this.result_highlight=null},s.prototype.results_show=function(){var e=this;if(e.is_multiple&&e.max_selected_options<=e.choices_count())return e.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1;e.results_showing=!0,e.search_field.focus(),e.search_field.val(e.search_field.val()),e.container.addClass("chosen-with-drop"),e.winnow_results(1);var s=e.drop_direction;if("function"==typeof s&&(s=s.call(this)),"auto"===s)if(e.drop_directionFixed)s=e.drop_directionFixed;else{var i=e.container.find(".chosen-drop"),r=i.outerHeight();e.drop_item_height&&r<e.max_drop_height&&(r=Math.min(e.max_drop_height,i.find(".chosen-results>.active-result").length*e.drop_item_height));var o=e.container.offset();o.top+r+30>t(window).height()+t(window).scrollTop()&&(s="up"),e.drop_directionFixed=s}return e.container.toggleClass("chosen-up","up"===s),e.autoResizeDrop(),e.form_field_jq.trigger("chosen:showing_dropdown",{chosen:e})},s.prototype.autoResizeDrop=function(){var e=this,s=e.max_drop_width;if(s){var i=e.container.find(".chosen-drop");i.removeClass("in");var r=0,o=i.find(".chosen-results"),n=o.children("li"),h=parseFloat(o.css("padding-left").replace("px","")),l=parseFloat(o.css("padding-right").replace("px","")),a=(isNaN(h)?0:h)+(isNaN(l)?0:l);n.each(function(){r=Math.max(r,t(this).outerWidth())}),i.css("width",Math.min(r+a+20,s)),e.fixDropWidthTimer=setTimeout(function(){e.fixDropWidthTimer=null,i.addClass("in"),e.winnow_results_set_highlight(1)},50)}},s.prototype.update_results_content=function(t){return this.search_results.html(t)},s.prototype.results_hide=function(){var t=this;return t.fixDropWidthTimer&&(clearTimeout(t.fixDropWidthTimer),t.fixDropWidthTimer=null),t.results_showing&&(t.result_clear_highlight(),t.container.removeClass("chosen-with-drop"),t.form_field_jq.trigger("chosen:hiding_dropdown",{chosen:t}),t.drop_directionFixed=0),t.results_showing=!1},s.prototype.set_tab_index=function(t){var e;if(this.form_field.tabIndex)return e=this.form_field.tabIndex,this.form_field.tabIndex=-1,this.search_field[0].tabIndex=e},s.prototype.set_label_behavior=function(){var e=this;if(this.form_field_label=this.form_field_jq.parents("label"),!this.form_field_label.length&&this.form_field.id.length&&(this.form_field_label=t("label[for='"+this.form_field.id+"']")),this.form_field_label.length>0)return this.form_field_label.bind("click.chosen",function(t){return e.is_multiple?e.container_mousedown(t):e.activate_field()})},s.prototype.show_search_field_default=function(){return this.is_multiple&&this.choices_count()<1&&!this.active_field?(this.search_field.val(this.default_text),this.search_field.addClass("default")):(this.search_field.val(""),this.search_field.removeClass("default"))},s.prototype.search_results_mouseup=function(e){var s;if(s=t(e.target).hasClass("active-result")?t(e.target):t(e.target).parents(".active-result").first(),s.length)return this.result_highlight=s,this.result_select(e),this.search_field.focus()},s.prototype.search_results_mouseover=function(e){var s;if(s=t(e.target).hasClass("active-result")?t(e.target):t(e.target).parents(".active-result").first())return this.result_do_highlight(s)},s.prototype.search_results_mouseout=function(e){if(t(e.target).hasClass("active-result"))return this.result_clear_highlight()},s.prototype.choice_build=function(e){var s,i,r=this;return s=t("<li />",{"class":"search-choice"}).html("<span title='"+e.html+"'>"+e.html+"</span>"),e.disabled?s.addClass("search-choice-disabled"):(i=t("<a />",{"class":"search-choice-close","data-option-array-index":e.array_index}),i.bind("click.chosen",function(t){return r.choice_destroy_link_click(t)}),s.append(i)),this.search_container.before(s)},s.prototype.choice_destroy_link_click=function(e){if(e.preventDefault(),e.stopPropagation(),!this.is_disabled)return this.choice_destroy(t(e.target))},s.prototype.choice_destroy=function(t){if(this.result_deselect(t[0].getAttribute("data-option-array-index")))return this.show_search_field_default(),this.is_multiple&&this.choices_count()>0&&this.search_field.val().length<1&&this.results_hide(),t.parents("li").first().remove(),this.search_field_scale()},s.prototype.results_reset=function(){var t=this.form_field_jq.val();this.reset_single_select_options(),this.form_field.options[0].selected=!0,this.single_set_selected_text(),this.show_search_field_default(),this.results_reset_cleanup();var e=this.form_field_jq.val(),s={selected:e};if(t===e||e.length||(s.deselected=t),this.form_field_jq.trigger("change",s),this.sync_sort_field(),this.active_field)return this.results_hide()},s.prototype.results_reset_cleanup=function(){return this.current_selectedIndex=this.form_field.selectedIndex,this.selected_item.find("abbr").remove()},s.prototype.result_select=function(t){var e,s;if(this.result_highlight)return e=this.result_highlight,this.result_clear_highlight(),this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple?e.removeClass("active-result"):this.reset_single_select_options(),s=this.results_data[e[0].getAttribute("data-option-array-index")],s.selected=!0,this.form_field.options[s.options_index].selected=!0,this.selected_option_count=null,this.is_multiple?this.choice_build(s):this.single_set_selected_text(s.text),(t.metaKey||t.ctrlKey)&&this.is_multiple||this.results_hide(),this.search_field.val(""),(this.is_multiple||this.form_field.selectedIndex!==this.current_selectedIndex)&&(this.form_field_jq.trigger("change",{selected:this.form_field.options[s.options_index].value}),this.sync_sort_field()),this.current_selectedIndex=this.form_field.selectedIndex,this.search_field_scale())},s.prototype.single_set_selected_text=function(t){return null==t&&(t=this.default_text),t===this.default_text?this.selected_item.addClass("chosen-default"):(this.single_deselect_control_build(),this.selected_item.removeClass("chosen-default")),this.compact_search&&this.search_field.attr("placeholder",t),this.selected_item.find("span").attr("title",t).text(t)},s.prototype.sync_sort_field=function(){var e=this;if(e.is_multiple&&e.sort_field){var s=t(e.sort_field);if(!s.length)return;var i=[];e.search_choices.find("li.search-choice").each(function(){var s=t(this),r=s.children(".search-choice-close").first().data("optionArrayIndex"),o=e.results_data[r];o&&o.selected&&i.push(o.value)}),s.val(i.join(e.sort_value_splitter)).trigger("change")}},s.prototype.result_deselect=function(t){var e;return e=this.results_data[t],!this.form_field.options[e.options_index].disabled&&(e.selected=!1,this.form_field.options[e.options_index].selected=!1,this.selected_option_count=null,this.result_clear_highlight(),this.results_showing&&this.winnow_results(),this.form_field_jq.trigger("change",{deselected:this.form_field.options[e.options_index].value}),this.sync_sort_field(),this.search_field_scale(),!0)},s.prototype.single_deselect_control_build=function(){if(this.allow_single_deselect)return this.selected_item.find("abbr").length||this.selected_item.find("span").first().after('<abbr class="search-choice-close"></abbr>'),this.selected_item.addClass("chosen-single-with-deselect")},s.prototype.get_search_text=function(){return this.search_field.val()===this.default_text?"":t("<div/>").text(t.trim(this.search_field.val())).html()},s.prototype.winnow_results_set_highlight=function(t){var e,s;if(s=this.is_multiple?[]:this.search_results.find(".result-selected.active-result"),e=s.length?s.first():this.search_results.find(".active-result").first(),null!=e)return this.result_do_highlight(e,t)},s.prototype.no_results=function(e){var s;return s=t('<li class="no-results">'+this.results_none_found+' "<span></span>"</li>'),s.find("span").first().html(e),this.search_results.append(s),this.form_field_jq.trigger("chosen:no_results",{chosen:this})},s.prototype.no_results_clear=function(){return this.search_results.find(".no-results").remove()},s.prototype.keydown_arrow=function(){var t;return this.results_showing&&this.result_highlight?(t=this.result_highlight.nextAll("li.active-result").first())?this.result_do_highlight(t):void 0:this.results_show()},s.prototype.keyup_arrow=function(){var t;return this.results_showing||this.is_multiple?this.result_highlight?(t=this.result_highlight.prevAll("li.active-result"),t.length?this.result_do_highlight(t.first()):(this.choices_count()>0&&this.results_hide(),this.result_clear_highlight())):void 0:this.results_show()},s.prototype.keydown_backstroke=function(){var t;return this.pending_backstroke?(this.choice_destroy(this.pending_backstroke.find("a").first()),this.clear_backstroke()):(t=this.search_container.siblings("li.search-choice").last(),t.length&&!t.hasClass("search-choice-disabled")?(this.pending_backstroke=t,this.single_backstroke_delete?this.keydown_backstroke():this.pending_backstroke.addClass("search-choice-focus")):void 0)},s.prototype.clear_backstroke=function(){return this.pending_backstroke&&this.pending_backstroke.removeClass("search-choice-focus"),this.pending_backstroke=null},s.prototype.keydown_checker=function(t){var e,s;switch(e=null!=(s=t.which)?s:t.keyCode,this.search_field_scale(),8!==e&&this.pending_backstroke&&this.clear_backstroke(),e){case 8:this.backstroke_length=this.search_field.val().length;break;case 9:this.results_showing&&!this.is_multiple&&this.result_select(t),this.mouse_on_container=!1;break;case 13:t.preventDefault();break;case 38:t.preventDefault(),this.keyup_arrow();break;case 40:t.preventDefault(),this.keydown_arrow()}},s.prototype.search_field_scale=function(){var e,s,i,r,o,n,h,l,a;if(this.is_multiple){for(i=0,h=0,o="position:absolute; left: -1000px; top: -1000px; display:none;",n=["font-size","font-style","font-weight","font-family","line-height","text-transform","letter-spacing"],l=0,a=n.length;l<a;l++)r=n[l],o+=r+":"+this.search_field.css(r)+";";return e=t("<div />",{style:o}),e.text(this.search_field.val()),t("body").append(e),h=e.width()+25,e.remove(),s=this.container.outerWidth(),h>s-10&&(h=s-10),this.search_field.css({width:h+"px"})}},s}(e),s.DEFAULTS=l,s.LANGUAGES=h,t.fn.chosen.Constructor=s}).call(this);