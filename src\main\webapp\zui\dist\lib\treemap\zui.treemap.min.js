/*!
 * ZUI: 树形图 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(e,t,a,o,r){"use strict";var n="zui.treemap",l={data:[],cableWidth:1,cableColor:"#808080",cableStyle:"solid",rowSpace:30,nodeSpace:20,listenNodeResize:!0,nodeTemplate:'<div class="treemap-node"><a class="treemap-node-wrapper"></a></div>',foldable:!0,clickNodeToFold:!0},d=function(t){return t.children("li,.treemap-data-item").map(function(){var t=e(this),a=t.data(),o=t.children(".text"),r=t.children(".content"),n=t.children("ul,.treemap-data-list");if(o.length&&(a.text=o.text()),r.length&&(a.html=r.html()),n.length&&(a.children=d(n)),!a.text&&!a.html){var l=t.children(":not(ul,.treemap-data-list)"),i=t.clone();i.find("ul,.treemap-data-list").remove(),l.length?a.html=i.html():a.text=i.text()}return a}).get()},i=function(t,a){var o=e(t);Array.isArray(a)&&(a={data:a}),a=e.extend({},l,o.data(),a);var r=a.data||[];if(!r.length){var n=o.children(".treemap-data");n.length&&(r=d(n.hide()))}var i=o.children(".treemap-nodes");i.length||(i=e('<div class="treemap-nodes" unselectable="on"/>').appendTo(o));var s=this;s.$=o,s.$nodes=i,s.data=Array.isArray(r)?r:[r],s.options=a,s.offsetX=0,s.offsetY=0,s.scale=a.scale||1,s.render(),i.on("resize",".treemap-node-wrapper",function(){s.delayDrawLines()}),a.foldable&&i.on("click",a.clickNodeToFold?".treemap-node-wrapper":".treemap-node-fold-icon",function(){s.toggle(e(this).closest(".treemap-node"))}),i.on("click",".treemap-node-wrapper",function(){var t=e(this).closest(".treemap-node");s.callEvent("onNodeClick",t.data("node"))})};i.prototype.toggle=function(e,t,a){var o=this;if("boolean"==typeof e&&(t=e,e=null),e||(e=o.$nodes.children(".treemap-node").first()),e){if(e.data("node").foldable===!1)return;t===r&&(t=e.hasClass("collapsed")),e.toggleClass("collapsed",!t).find('[data-toggle="tooltip"]').tooltip("hide"),a||e.addClass("tree-node-collapsing"),o.$nodes.find(".tooltip").remove(),o.drawLines(),a?(clearTimeout(o.toggleTimeTask),o.toggleTimeTask=setTimeout(function(){e.removeClass("tree-node-collapsing")},200)):e.removeClass("tree-node-collapsing")}},i.prototype.showLevel=function(t){var a=this;a.$nodes.find(".treemap-node").each(function(){var o=e(this);a.toggle(o,o.data("level")<t,!0)})},i.prototype.render=function(e){var t=this;t.data=e?Array.isArray(e)?e:[e]:t.data,t.data&&(t.createNodes(),t.drawLines(),t.delayDrawLines(500)),t.callEvent("afterRender")},i.prototype.createNodes=function(t,a,r){var l=this,d=l.options,i=d.rowSpace,s=l.$nodes;a||(s.find(".treemap-node-wrapper").off("resize."+n),s.empty()),d.sort&&t.sort("function"==typeof d.sort?d.sort:function(e,t){return(e.order||0)-t.order});var p=null;t=t||l.data,a||(l.maxLevel=1),e.each(t,function(c,h){"string"==typeof h&&(h={html:h},t[c]=h),h.id||(h.id=e.zui.uuid()),h.level=a?a.level+1:1,l.maxLevel=o.max(l.maxLevel,h.level);var f="function"==typeof d.nodeTemplate,m=f?d.nodeTemplate(h,l):e(d.nodeTemplate),g=m.find(".treemap-node-wrapper");g.length||(g=e('<div class="treemap-node-wrapper"/>').appendTo(m));var v=h.children,u=v&&v.length;h.isOnlyOneChild=1===u,h.idx=c;var y=a?a.row+1:0;m.toggleClass("treemap-node-has-child",!!u).toggleClass("treemap-node-has-parent",!!a).toggleClass("treemap-node-one-child",1===u).toggleClass("collapsed",!!h.collapsed&&"false"!==h.collapsed).toggleClass("treemap-node-root",!y).attr({"data-id":h.id,"data-level":h.level}).data("node",h),h.className&&m.addClass(h.className),h.row=y;var w=e.extend({},d.nodeStyle,h.style);h.textColor&&(w.color=h.textColor),h.color&&(w.backgroundColor=h.color),h.border&&(w.border=h.border);var b=e.extend({},h.attrs,{title:h.caption});if(h.tooltip&&(b["data-toggle"]="tooltip",b.title=h.tooltip),g.attr(b).css(w),p&&m.css("padding-left",d.nodeSpace),f||(h.html?g.append(h.html):h.text&&g.text(h.text)),m.appendTo(a?a.$children:s),p&&(p.next=h),h.prev=p,h.parent=a,h.$=m,h.$wrapper=g,u){var x=m.find(".treemap-node-children");x.length||(x=e('<div class="treemap-node-children"/>').appendTo(m)),x.css("margin-top",i),h.$children=x,l.createNodes(v,h)}d.listenNodeResize&&g.on("resize."+n,function(){l.delayDrawLines()}),p=h,r&&r(m,h)}),a||s.find('[data-toggle="tooltip"]').tooltip(d.tooltip)},i.prototype.delayDrawLines=function(e){var t=this;clearTimeout(t.delayDrawLinesTask),t.delayDrawLinesTask=setTimeout(function(){t.drawLines()},e||10)},i.prototype.drawLines=function(t,a){var r=this,n=r.options,l=n.rowSpace,d={};n.cableWidth&&(d.borderWidth=n.cableWidth),n.cableStyle&&(d.borderStyle=n.cableStyle),n.cableColor&&(d.borderColor=n.cableColor);var i=o.round(l/2),s=r.$nodes.offset().left;e.each(t||r.data,function(t,p){var c=p.$wrapper,h=p.children,f=e.extend({height:i,top:-i-1,left:o.round((c.outerWidth()-d.borderWidth)/2),color:d.borderColor},d);if(a&&!a.isOnlyOneChild){var m=c.find(".treemap-line-top");m.length||(m=e('<div class="treemap-line-top"/>').appendTo(c)),m.css(f)}if(h&&h.length){f.top=c.outerHeight()-1,p.isOnlyOneChild&&(f.height=l);var g=c.find(".treemap-line-bottom");if(g.length||(g=e('<div class="treemap-line-bottom"/>').appendTo(c),n.foldable&&g.append('<i class="treemap-node-fold-icon icon" style="transform: translate(-'+o.floor(f.borderWidth/2)+"px, "+i+'px)"/>')),g.css(f),r.drawLines(h,p),h.length>1){var v=h[0],u=h[h.length-1],y=p.$.children(".treemap-line");y.length||(y=e('<div class="treemap-line"/>').insertAfter(c));var w=o.round(v.$wrapper.offset().left-s+v.$wrapper.outerWidth()/2);y.css(e.extend({marginTop:i,left:w,width:u.$wrapper.offset().left-s-w+u.$wrapper.outerWidth()/2},d))}}}),a||r.callEvent("afterDrawLines")},i.prototype.callEvent=function(e,t){var a=this;if(Array.isArray(t)||(t=[t]),a.$.trigger(e,t),"function"==typeof a.options[e])return a.options[e].apply(a,t)},i.DEFAULTS=l,i.NAME=n,e.fn.treemap=function(t,a,o){return this.each(function(){var r=e(this),l=r.data(n),d="object"==typeof t&&t;l||r.data(n,l=new i(this,d)),"string"==typeof t&&l[t](a,o)})},e.fn.treemap.Constructor=i}(jQuery,window,document,Math,void 0);