/*!
 * ZUI: 日历 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */

.calendar {
  margin-bottom: 20px;
  }
.calendar > header {
  margin-bottom: 10px;
  }
.calendar > header .btn-toolbar > .btn-group {
  margin-right: 10px;
  }
.calendar > header .calendar-caption {
  line-height: 30px;
  }
.calendar .table {
  margin-bottom: 0;
  table-layout: fixed;
  }
.calendar .table > thead > tr > th,
.calendar .table > tbody > tr > td {
  width: 14.28571429%;
  padding: 0;
  }
.calendar .table > thead > tr > th {
  color: #808080;
  text-align: center;
  background-color: #fff;
  }
.calendar .weekends-empty .table > thead > tr > th,
.calendar .weekends-empty .table > tbody > tr > td {
  width: 20%;
  }
.calendar .weekends-empty .table > thead > tr > th.weekend-head,
.calendar .weekends-empty .table > tbody > tr > td.weekend-day {
  width: 60px;
  min-width: 40px;
  }
.calendar .day {
  opacity: .7;
  }
.calendar .day > .heading {
  padding: 2px 5px;
  text-align: right;
  }
.calendar .day > .heading > .month {
  padding: 1px 2px;
  color: #fff;
  white-space: nowrap;
  background-color: #b3b3b3;
  border-radius: 3px;
  }
.calendar .day > .content {
  height: 100%;
  min-height: 70px;
  }
.calendar .cell-day {
  background-color: #f1f1f1;
  }
.calendar .cell-day.past > .day > .content {
  opacity: .7;
  }
.calendar .cell-day.current-month {
  background: none;
  }
.calendar .cell-day.current-month > .day {
  opacity: 1;
  }
.calendar .cell-day.current {
  background-color: #fff0d5;
  -webkit-box-shadow: inset 1px 1px 0 #808080, inset -1px -1px 0 #808080;
          box-shadow: inset 1px 1px 0 #808080, inset -1px -1px 0 #808080;
  }
.calendar .cell-day.current > .day > .content {
  padding: 0;
  }
.calendar .cell-day.current > .day > .heading {
  background-color: rgba(0, 0, 0, .1);
  }
.calendar .cell-day.drop-to {
  background-color: #fff0d5;
  opacity: 1;
  }
.calendar .event {
  padding: 1px 5px;
  margin: 0 1px 1px;
  color: #fff;
  cursor: pointer;
  background-color: #3280fc;
  opacity: .95;
  -webkit-transition: all .4s cubic-bezier(.175, .885, .32, 1);
       -o-transition: all .4s cubic-bezier(.175, .885, .32, 1);
          transition: all .4s cubic-bezier(.175, .885, .32, 1);
  }
a.calendar .event:hover {
  background-color: #0462f7;
  }
.calendar .event:hover {
  opacity: 1;
  }
.calendar .event.drag-shadow {
  cursor: move;
  }
.calendar .event.drag-from {
  opacity: .25;
  }
.calendar .event.color-red {
  color: #fff;
  background-color: #ea644a;
  }
a.calendar .event.color-red:hover {
  background-color: #e53d1c;
  }
.calendar .event.color-green {
  color: #fff;
  background-color: #38b03f;
  }
a.calendar .event.color-green:hover {
  background-color: #2c8931;
  }
.calendar .event.color-yellow {
  color: #fff;
  background-color: #f1a325;
  }
a.calendar .event.color-yellow:hover {
  background-color: #d5890e;
  }
.calendar .event.color-blue {
  color: #fff;
  background-color: #03b8cf;
  }
a.calendar .event.color-blue:hover {
  background-color: #028b9d;
  }
.calendar .event.color-brown {
  color: #fff;
  background-color: #bd7b46;
  }
a.calendar .event.color-brown:hover {
  background-color: #996337;
  }
.calendar .event.color-purple {
  color: #fff;
  background-color: #8666b8;
  }
a.calendar .event.color-purple:hover {
  background-color: #6c4aa1;
  }
.calendar.limit-event-title .event {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  }
