/*!
 * ZUI: 日历 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t,e){"use strict";var a="zui.calendar",n="number",s="string",o="undefined",i={primary:1,green:2,red:3,blue:4,yellow:5,brown:6,purple:7},r=function(t,e){e=e||1;for(var a=t.clone();a.getDay()!=e;)a.addDays(-1);return a.clearTime(),a},d=function(t){var e=t.clone();return e.setDate(1),e},l=function(t,e){var a=t.clone().clearTime(),n=e.clone().clearTime();return Math.round((n.getTime()-a.getTime())/Date.ONEDAY_TICKS)+1},c=function(t,e,a){for(var n=t.clone(),s=0;n<=e;)a(n.clone(),s++),n.addDays(1)},h=function(e,n){if(this.name=a,this.$=t(e),this.id=this.$.attr("id")||a+t.zui.uuid(),this.$.attr("id",this.id),this.storeName=a+"."+this.id,n=this.getOptions(n),this.getLang(),this.resetData(n.data),this.storeData=t.zui.store.pageGet(this.storeName,{date:"today",view:"month"}),this.date=n.startDate||"today",this.view=n.startView||"month",this.$.toggleClass("limit-event-title",n.limitEventTitle),n.withHeader){var s=this.$.children(".calender-header");s.length||(s=t('<header class="calender-header"><div class="btn-toolbar"><div class="btn-group"><button type="button" class="btn btn-today">{today}</button></div><div class="btn-group"><button type="button" class="btn btn-prev"><i class="icon-chevron-left"></i></button><button type="button" class="btn btn-next"><i class="icon-chevron-right"></i></button></div><div class="btn-group"><span class="calendar-caption"></span></div></div></header>'.format(this.lang)),this.$.append(s)),this.$caption=s.find(".calendar-caption"),this.$todayBtn=s.find(".btn-today"),this.$header=s}var o=this.$.children(".calendar-views");o.length||(o=t('<div class="calendar-views"></div>'),this.$.append(o)),this.$views=o,this.display(),this.bindEvents()};h.DEFAULTS={langs:{zh_cn:{weekNames:["周一","周二","周三","周四","周五","周六","周日"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],today:"今天",year:"{0}年",month:"{0}月",yearMonth:"{0}年{1}月"},zh_tw:{weekNames:["週一","週二","週三","週四","週五","週六","週日"],monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],today:"今天",year:"{0}年",month:"{0}月",yearMonth:"{0}年{1}月"},en:{weekNames:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],monthNames:["Jan","Feb","Mar","Apr","May","June","July","Aug","Sep","Oct","Nov","Dec"],today:"Today",year:"{0}",month:"{0}",yearMonth:"{2}, {0}"}},data:{calendars:{defaultCal:{color:"#229F24"}},events:[]},limitEventTitle:!0,storage:!0,withHeader:!0,dragThenDrop:!0},h.prototype.resetData=function(t){var e=this;e.data=t=t||e.data,t.calendars&&(this.calendars={},e.addCalendars(t.calendars,!0)),t.events&&(this.events=[],e.addEvents(t.events,!0)),e.sortEvents()},h.prototype.sortEvents=function(){var t=this,e=t.events;Array.isArray(e)||(e=[]),e.sort(t.options.eventSorter||function(t,e){if(t.allDay)return 1;if(e.allDay)return-1;var a=t.start>e.start?1:t.start<e.start?-1:0;return 0===a&&(a=t.id<e.id?-1:1),a}),t.events=e},h.prototype.bindEvents=function(){var e=this.$,a=this;e.on("click",".btn-today",function(){a.date=new Date,a.display(),e.callComEvent(a,"clickTodayBtn")}).on("click",".btn-next",function(){"month"===a.view&&a.date.addMonths(1),a.display(),e.callComEvent(a,"clickNextBtn")}).on("click",".btn-prev",function(){"month"===a.view&&a.date.addMonths(-1),a.display(),e.callComEvent(a,"clickPrevBtn")}).on("click",".event",function(n){n.stopPropagation(),t(n.target).closest(".event-btn").length||e.callComEvent(a,"clickEvent",{element:this,event:t(this).data("event"),events:a.events},n)}).on("click",".cell-day",function(n){e.callComEvent(a,"clickCell",{element:this,view:a.view,date:new Date(t(this).children(".day").attr("data-date")),events:a.events},n)})},h.prototype.addCalendars=function(e,a){var n=this;n.calendars||(n.calendars={}),t.isPlainObject(e)&&(e=[e]),t.each(e,function(e,s){if(a||!1!==n.$.callComEvent(this,"beforeAddCalendars",{newCalendar:s,data:n.data})){if(s.color||(s.color="primary"),i[s.color.toLowerCase()])s.presetColor=!0;else{var o=new t.zui.Color(s.color);s.textColor=o.contrast().hexStr()}n.calendars[s.name]=s}}),a||(n.display(),n.$.callComEvent(n,"addCalendars",{newCalendars:e,data:n.data}))},h.prototype.addEvents=function(e,a){var i=this;i.events||(i.events=[]),t.isPlainObject(e)&&(e=[e]),t.each(e,function(e,r){if(a||!1!==i.$.callComEvent(i,"beforeAddEvent",{newEvent:r,data:i.data})){var d=typeof r.start,c=typeof r.end;d!==n&&d!==s||(r.start=new Date(r.start)),c!==n&&c!==s||(r.end=new Date(r.end)),typeof r.id===o&&(r.id=t.zui.uuid()),r.allDay&&(r.start.clearTime(),r.end||(r.end=r.start.clone()),r.end.clearTime().addDays(1).addMilliseconds(-1)),r.days=l(r.start,r.end),i.events.push(r)}}),a||(i.sortEvents(),i.display(),i.$.callComEvent(i,"addEvents",{newEvents:e,data:i.data}))},h.prototype.getEvent=function(t){for(var e=this.events,a=0;a<e.length;a++)if(e[a].id==t)return e[a];return null},h.prototype.updateEvents=function(e){var a={data:this.data,changes:[]},n=this;t.isPlainObject(e)&&(e=[e]);var o,i,r;t.each(e,function(e,d){o=d.event,i=d.changes,r={event:o,changes:[]},typeof o===s&&(o=n.getEvent(o)),o&&(t.isPlainObject(i)&&(i=[i]),t.each(d,function(e,a){!1!==n.$.callComEvent(n,"beforeChange",{event:o,change:a.change,to:a.to,from:o[a.change]})&&(r.changes.push(t.extend(!0,{},a,{from:o[a.change]})),o[a.change]=a.to)})),a.changes.push(r)}),n.sortEvents(),n.display(),n.$.callComEvent(n,"change",a)},h.prototype.removeEvents=function(e){Array.isArray(e)||(e=[e]);var a,n,s,o=this.events,i=this,r=[];t.each(e,function(e,d){a=t.isPlainObject(d)?d.id:d,s=-1;for(var l=0;l<o.length;l++)if(o[l].id==a){s=l,n=o[l];break}s>=0&&fasle!==i.$.callComEvent(i,"beforeRemoveEvent",{event:n,eventId:a,data:i.data})&&(o.splice(s,1),r.push(n))}),i.sortEvents(),i.display(),i.$.callComEvent(i,"removeEvents",{removedEvents:r,data:i.data})},h.prototype.getOptions=function(e){return this.options=t.extend(!0,{},h.DEFAULTS,this.$.data(),e,!0),this.options},h.prototype.getLang=function(){this.langName=this.options.lang||t.zui.clientLang(),this.lang=t.zui.getLangData(a,this.langName,this.options.langs)},h.prototype.display=function(e,a){var n=this,i=typeof e,r=typeof a;i===o?e=n.view:n.view=e,r===o?a=n.date:n.date=a,"today"===a&&(a=new Date,n.date=a),typeof a===s&&(a=new Date(a),n.date=a),n.options.storage&&t.zui.store.pageSet(n.storeName,{date:a,view:e});var d={view:e,date:a},l=function(){switch(e){case"month":n.displayMonth(a)}n.$.callComEvent(n,"display",d)},c=n.$.callComEvent(n,"beforeDisplay",[d,l]);c!==!1&&l()},h.prototype.displayMonth=function(e){var a=this;e=e||a.date;var n,s=a.options,o=a.lang,i=a.$views,l=(a.$,a.$monthView);if(!l||!l.length){l=t('<div class="calendar-view month"><table class="table table-bordered"><thead><tr class="week-head"></tr></thead><tbody class="month-days"></tbody></table></div>');var c,h=l.find(".week-head"),v=l.find(".month-days");for(n=0;n<7;n++)t("<th>"+o.weekNames[n]+"</th>").toggleClass("weekend-head",n>=5).appendTo(h);for(n=0;n<6;n++){c=t('<tr class="week-days"></tr>');for(var p=0;p<7;p++)t('<td class="cell-day"><div class="day"><div class="heading"><span class="month"></span> <span class="number"></span></div><div class="content"><div class="events"></div></div></div></td>').toggleClass("weekend-day",p>=5).appendTo(c);v.append(c)}i.append(l),a.$monthView=l}var g,u,y,f,m,b,w,C,E,D,$,k=l.find(".week-days"),T=d(e),M=new Date,x=r(T),N=e.getFullYear(),P=e.getMonth(),S=M.getMonth(),z=M.getFullYear(),A=M.getDate(),F=x.clone().addDays(42).addMilliseconds(-1),O=x.clone().addDays(1).addMilliseconds(-1),j=a.getEvents(x,F),L=a.calendars,B=!0;if(k.each(function(e){g=t(this),g.find(".day").each(function(i){C=0===i,u=t(this),y=u.closest(".cell-day"),f=O.getFullYear(),m=O.getDate(),b=O.getMonth(),w=O.toDateString(),u.attr("data-date",w).data("date",O.clone()),u.find(".heading > .number").text(m).toggle(!s.hideFirstDayNumber||1!==m),u.find(".heading > .month").toggle(0===e&&0===i||1===m).text((0===b&&1===m?o.year.format(f)+" ":"")+o.monthNames[b]),y.toggleClass("current-month",b===P),y.toggleClass("current",m===A&&b===S&&f===z),y.toggleClass("past",O<M),y.toggleClass("first-day",1===m),y.toggleClass("future",O>M),$=u.find(".events").empty();var r=j[w];if(r){var d,l=r.events,c=0;for(n=0;n<=r.maxPos;++n)d=l[n],!d||d.placeholder&&!C?c++:(B&&i>=5&&(B=!1),s.eventCreator?E=s.eventCreator(d,y,a):(E=t('<div data-id="'+d.id+'" class="event" title="'+d.desc+'"><span class="time">'+d.start.format("hh:mm")+'</span> <span class="title">'+d.title+"</span></div>"),E.find(".time").toggle(!d.allDay),E.data("event",d),E.attr("data-days",d.days)),E.toggleClass("event-all-day",!!d.allDay).data("event",d).attr("data-days",d.days),d.calendar&&(D=L[d.calendar],D&&(D.presetColor?E.addClass("color-"+D.color):E.css({"background-color":D.color,color:D.textColor}))),d.days&&(d.placeholder?C&&E.css("width",Math.min(7,d.days-d.holderPos)+"00%"):E.css("width",Math.min(7-i,d.days)+"00%")),c>0&&(E.css("margin-top",22*c),c=0),$.append(E))}y.toggleClass("empty",!u.find(".events").length),s.dayFormater&&s.dayFormater(y,O,r,a),O.addDays(1)})}),s.hideEmptyWeekends&&l.toggleClass("weekends-empty",B),s.withHeader&&(a.$caption.text(o.yearMonth.format(N,P+1,o.monthNames[P])),a.$todayBtn.toggleClass("disabled",P===S&&N===z)),s.dragThenDrop){if(!t.fn.droppable)return console.error("Calendar dragThenDrop option requires droppable.js");l.data("zui.droppable")||l.droppable(t.extend({target:".cell-day",selector:".event",flex:!0,start:function(){a.$.addClass("event-dragging")},drop:function(t){var e=t.element.data("event"),n=t.target.children(".day").attr("data-date");if(e&&n){var s=e.start.clone();if(s.toDateString()!=n&&(n=new Date(n),n.setHours(s.getHours()),n.setMinutes(s.getMinutes()),n.setSeconds(s.getSeconds()),!1!==a.$.callComEvent(a,"beforeChange",{event:e,change:"start",to:n}))){var o=e.end.clone();e.end.addMilliseconds(e.end.getTime()-s.getTime()),e.start=n,a.display(),a.$.callComEvent(a,"change",{data:a.data,changes:[{event:e,changes:[{change:"start",from:s,to:e.start},{change:"end",from:o,to:e.end}]}]})}}},finish:function(){a.$.removeClass("event-dragging")}},t.isPlainObject(s.dragThenDrop)?s.dragThenDrop:null))}},h.prototype.getEvents=function(e,a){var n={},s=(this.calendars,function(t,e,a){var s=t.toDateString(),o=n[s];if(o||(o={maxPos:-1,events:{}}),"undefined"==typeof a)for(var i=0;i<100;++i)if(!o.events[i]){a=i;break}return o.maxPos=Math.max(a,o.maxPos),o.events[a]=e,n[s]=o,a});return t.each(this.events,function(n,o){if(o.start>=e&&o.start<=a){var i=s(o.start,o);if(o.days>1){var r=t.extend({placeholder:!0},o);c(o.start.clone().addDays(1),o.end,function(e,a){s(e.clone(),t.extend({holderPos:a},r),i)})}}}),n},t.fn.calendar=function(e){return this.each(function(){var n=t(this),o=n.data(a),i="object"==typeof e&&e;o||n.data(a,o=new h(this,i)),typeof e==s&&o[e]()})},t.fn.calendar.Constructor=h}(jQuery,window);