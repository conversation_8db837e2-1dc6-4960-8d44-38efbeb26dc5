package com.ruoyi.dao.impl;

import com.ruoyi.dao.ParcelDao;
import com.ruoyi.entity.Parcel;
import com.ruoyi.jdbc.JdbcUtil;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

public class ParcelImpl implements ParcelDao {
    public boolean addParcel(Parcel parcel, Integer userId) throws SQLException, ClassNotFoundException {
        String[] name={"SF","ZTO","YTO","YUNDA","JD","EMS","顺丰"};
        String sql = "INSERT INTO t_parcel(" +
                "sender_name, " +
                "sender_phone, " +
                "sender_address, " +
                "receiver_name, " +
                "receiver_phone, " +
                "receiver_address, " +
                "parcel_type, " +
                "weight, " +
                "price, " +
                "status, " +
                "remark, " +
                "create_time, " +
                "update_time, " +
                "parcel_no, " +
                "user_id" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Connection connection = JdbcUtil.getConnection();
        String Name=name[new Random().nextInt(name.length)];
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String createTime=sdf.format(new Date());
        String parcelNo=Name+createTime+new Random().nextInt(100000000);
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, parcel.getSenderName());
        preparedStatement.setString(2, parcel.getSenderPhone());
        preparedStatement.setString(3, parcel.getSenderAddress());
        preparedStatement.setString(4, parcel.getReceiverName());
        preparedStatement.setString(5, parcel.getReceiverPhone());
        preparedStatement.setString(6, parcel.getReceiverAddress());
        preparedStatement.setString(7, parcel.getParcelType()); 
        preparedStatement.setBigDecimal(8, new BigDecimal(parcel.getWeight()));
        preparedStatement.setBigDecimal(9, new BigDecimal(parcel.getPrice()));
        preparedStatement.setInt(10, parcel.getStatus());
        preparedStatement.setString(11, parcel.getRemark());
        preparedStatement.setTimestamp(12, parcel.getCreateTime());
        preparedStatement.setTimestamp(13, parcel.getUpdateTime());
        preparedStatement.setString(14, parcelNo);
        preparedStatement.setInt(15, userId);
        int i = preparedStatement.executeUpdate();
        JdbcUtil.close(connection, preparedStatement,null);
        if (i>0) return true;
        return false;
    }
    public List<Parcel> getParcelList(Integer userId) throws SQLException, ClassNotFoundException {
        String sql = "SELECT * FROM t_parcel WHERE user_id = ?";
        Connection connection = JdbcUtil.getConnection();
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setInt(1, userId);
        ResultSet resultSet = preparedStatement.executeQuery();
        List<Parcel> parcelList = new ArrayList<>();
        while (resultSet.next()) {
            Parcel parcel = new Parcel();
            parcel.setParcelId(resultSet.getInt("parcel_id"));
            parcel.setParcelNo(resultSet.getString("parcel_no"));
            parcel.setReceiverName(resultSet.getString("receiver_name"));
            parcel.setParcelType(resultSet.getString("parcel_type"));
            parcel.setStatus(resultSet.getInt("status"));
            parcelList.add(parcel);
        }
        JdbcUtil.close(connection, preparedStatement, resultSet);
        return parcelList;
    }
}
