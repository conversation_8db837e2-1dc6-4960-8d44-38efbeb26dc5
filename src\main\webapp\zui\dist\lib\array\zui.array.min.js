/*!
 * ZUI: 数组辅助方法 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(){"use strict";var r="function";Array.prototype.lastIndexOf||(Array.prototype.lastIndexOf=function(r){var t=this.length,e=Number(arguments[1]);for(isNaN(e)?e=t-1:(e=e<0?Math.ceil(e):Math.floor(e),e<0?e+=t:e>=t&&(e=t-1));e>-1;e--)if(e in this&&this[e]===r)return e;return-1}),Array.prototype.every||(Array.prototype.every=function(t){var e=this.length;if(typeof t!=r)throw new TypeError;for(var o=arguments[1],n=0;n<e;n++)if(n in this&&!t.call(o,this[n],n,this))return!1;return!0}),Array.prototype.filter||(Array.prototype.filter=function(t){var e=this.length;if(typeof t!=r)throw new TypeError;for(var o=[],n=arguments[1],i=0;i<e;i++)if(i in this){var f=this[i];t.call(n,f,i,this)&&o.push(f)}return o}),Array.prototype.indexOf||(Array.prototype.indexOf=function(r){var t=this.length,e=Number(arguments[1])||0;for(e=e<0?Math.ceil(e):Math.floor(e),e<0&&(e+=t);e<t;e++)if(e in this&&this[e]===r)return e;return-1}),Array.prototype.map||(Array.prototype.map=function(t){var e=this.length;if(typeof t!=r)throw new TypeError;for(var o=new Array(e),n=arguments[1],i=0;i<e;i++)i in this&&(o[i]=t.call(n,this[i],i,this));return o}),Array.prototype.where||(Array.prototype.where=function(t,e){e=e||[];var o,n,i;return this.forEach(function(f){n=!0;for(var a in t)if(o=t[a],typeof o===r?n=o(f):(i=f[a],n=i&&i===o),!n)break;n&&e.push(f)}),e}),Array.prototype.groupBy||(Array.prototype.groupBy=function(r){var t={};return this.forEach(function(e){var o=e[r];o||(o="unkown"),t[o]||(t[o]=[]),t[o].push(e)}),t}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(r){if(null==this)throw new TypeError('"this" is null or not defined');var t=Object(this),e=t.length>>>0;if("function"!=typeof r)throw new TypeError("predicate must be a function");for(var o=arguments[1],n=0;n<e;){var i=t[n];if(r.call(o,i,n,t))return i;n++}}}),Array.prototype.findIndex||Object.defineProperty(Array.prototype,"findIndex",{value:function(r){if(null==this)throw new TypeError('"this" is null or not defined');var t=Object(this),e=t.length>>>0;if("function"!=typeof r)throw new TypeError("predicate must be a function");for(var o=arguments[1],n=0;n<e;){var i=t[n];if(r.call(o,i,n,t))return n;n++}return-1}})}();