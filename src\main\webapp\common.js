/**
 * 公共JavaScript函数库
 * 包含检查登录状态等通用功能
 */

// 检查用户登录状态
function checkLoginStatus() {
    return $.ajax({
        url: 'login',
        type: 'POST',
        data: {
            action: 'checkStatus'
        },
        success: function(response) {
            if (response && response.username) {
                // 已登录
                $('#userInfo').html("<a href='#'>欢迎, " + response.username + "</a>");
                $('#loginInfo').hide();
                $('#welcomeInfo').show();
                // 显示"我的快递"按钮
                $('#my-parcel-btn').show();
                return true;
            } else {
                // 未登录
                $('#loginInfo').show();
                $("#welcomeInfo").hide();
                // 隐藏"我的快递"按钮
                $('#my-parcel-btn').hide();
                // 如果本地存储有用户名但服务器会话已过期，清除本地存储
                if (localStorage.getItem("username")) {
                    localStorage.removeItem("username");
                }
                return false;
            }
        },
        error: function() {
            // 请求失败，假定未登录
            $('#loginInfo').show();
            $("#welcomeInfo").hide();
            // 隐藏"我的快递"按钮
            $('#my-parcel-btn').hide();
            localStorage.removeItem("username");
            return false;
        }
    });
}

// 添加登出功能
function setupLogout() {
    $('#userInfo').on('click', 'a', function(e) {
        e.preventDefault();
        $.post('login', {
            action: 'logout'
        }, function(response) {
            if (response === "注销成功") {
                localStorage.removeItem("username");
                var myMessager = new $.zui.Messager({ type: 'success' });
                myMessager.show('已成功退出登录');
                // 重新检查登录状态
                checkLoginStatus();
            }
        });
    });
}

// 初始化所有通用功能
function initCommonFeatures() {
    // 检查登录状态
    checkLoginStatus();
    
    // 设置登出功能
    setupLogout();
}