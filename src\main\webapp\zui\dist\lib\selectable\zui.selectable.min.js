/*!
 * ZUI: 拖拽选择 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t){"use strict";var e="zui.selectable",i=function(i,n){this.name=e,this.$=t(i),this.id=t.zui.uuid(),this.selectOrder=1,this.selections={},this.getOptions(n),this._init()},n=function(t,e,i){return t>=i.left&&t<=i.left+i.width&&e>=i.top&&e<=i.top+i.height},o=function(t,e){var i=Math.max(t.left,e.left),o=Math.max(t.top,e.top),s=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height);return n(i,o,t)&&n(s,l,t)&&n(i,o,e)&&n(s,l,e)};i.DEFAULTS={selector:"li,tr,div",trigger:"",selectClass:"active",rangeStyle:{border:"1px solid "+(t.zui.colorset?t.zui.colorset.primary:"#3280fc"),backgroundColor:t.zui.colorset?new t.zui.Color(t.zui.colorset.primary).fade(20).toCssStr():"rgba(50, 128, 252, 0.2)"},clickBehavior:"toggle",ignoreVal:3,listenClick:!0},i.prototype.getOptions=function(e){this.options=t.extend({},i.DEFAULTS,this.$.data(),e)},i.prototype.select=function(t){this.toggle(t,!0)},i.prototype.unselect=function(t){this.toggle(t,!1)},i.prototype.toggle=function(e,i,n){var o,s,l=this.options.selector,c=this;if(void 0===e)return void this.$.find(l).each(function(){c.toggle(this,i)});if("object"==typeof e?(o=t(e).closest(l),s=o.data("id")):(s=e,o=c.$.find('.slectable-item[data-id="'+s+'"]')),o&&o.length){if(s||(s=t.zui.uuid(),o.attr("data-id",s)),void 0!==i&&null!==i||(i=!c.selections[s]),!!i!=!!c.selections[s]){var a;"function"==typeof n&&(a=n(i)),a!==!0&&(c.selections[s]=!!i&&c.selectOrder++,c.callEvent(i?"select":"unselect",{id:s,selections:c.selections,target:o,selected:c.getSelectedArray()},c))}c.options.selectClass&&o.toggleClass(c.options.selectClass,i)}},i.prototype.getSelectedArray=function(){var e=[];return t.each(this.selections,function(t,i){i&&e.push(t)}),e},i.prototype.syncSelectionsFromClass=function(){var e=this,i=e.$children=e.$.find(e.options.selector);e.selections={},i.each(function(){var i=t(this);e.selections[i.data("id")]=i.hasClass(e.options.selectClass)})},i.prototype._init=function(){var e,i,n,s,l,c,a,r=this.options,u=this,h=r.ignoreVal,d=!0,f="."+this.name+"."+this.id,g="function"==typeof r.checkFunc?r.checkFunc:null,p="function"==typeof r.rangeFunc?r.rangeFunc:null,v=!1,y=null,m="mousedown"+f,b=function(){s&&u.$children.each(function(){var e=t(this),i=e.offset();i.width=e.outerWidth(),i.height=e.outerHeight();var n=p?p.call(this,s,i):o(s,i);if(g){var l=g.call(u,{intersect:n,target:e,range:s,targetRange:i});l===!0?u.select(e):l===!1&&u.unselect(e)}else n?u.select(e):u.multiKey||u.unselect(e)})},C=function(o){v&&(l=o.pageX,c=o.pageY,s={width:Math.abs(l-e),height:Math.abs(c-i),left:l>e?e:l,top:c>i?i:c},d&&s.width<h&&s.height<h||(n||(n=t('.selectable-range[data-id="'+u.id+'"]'),n.length||(n=t('<div class="selectable-range" data-id="'+u.id+'"></div>').css(t.extend({zIndex:1060,position:"absolute",top:e,left:i,pointerEvents:"none"},u.options.rangeStyle)).appendTo(t("body")))),n.css(s),clearTimeout(a),a=setTimeout(b,10),d=!1))},w=function(e){t(document).off(f),clearTimeout(y),v&&(v=!1,n&&n.remove(),d||s&&(clearTimeout(a),b(),s=null),u.callEvent("finish",{selections:u.selections,selected:u.getSelectedArray()}),e.preventDefault())},$=function(o){if(v)return w(o);var s=t.zui.getMouseButtonCode(r.mouseButton);if(!(s>-1&&o.button!==s||u.altKey||3===o.which||u.callEvent("start",o)===!1)){var l=u.$children=u.$.find(r.selector);l.addClass("slectable-item");var c=u.multiKey?"multi":r.clickBehavior;if("single"===c&&u.unselect(),r.listenClick&&("multi"===c?u.toggle(o.target):"single"===c?u.select(o.target):"toggle"===c&&u.toggle(o.target,null,function(t){u.unselect()})),u.callEvent("startDrag",o)===!1)return void u.callEvent("finish",{selections:u.selections,selected:u.getSelectedArray()});e=o.pageX,i=o.pageY,n=null,d=!0,v=!0,t(document).on("mousemove"+f,C).on("mouseup"+f,w),y=setTimeout(function(){t(document).on(m,w)},10),o.preventDefault()}},k=r.container&&"default"!==r.container?t(r.container):this.$;r.trigger?k.on(m,r.trigger,$):k.on(m,$),t(document).on("keydown",function(t){var e=t.keyCode;17===e||91==e?u.multiKey=e:18===e&&(u.altKey=!0)}).on("keyup",function(t){u.multiKey=!1,u.altKey=!1})},i.prototype.callEvent=function(e,i){var n=t.Event(e+"."+this.name);this.$.trigger(n,i);var o=n.result,s=this.options[e];return"function"==typeof s&&(o=s.apply(this,Array.isArray(i)?i:[i])),o},t.fn.selectable=function(n){return this.each(function(){var o=t(this),s=o.data(e),l="object"==typeof n&&n;s||o.data(e,s=new i(this,l)),"string"==typeof n&&s[n]()})},t.fn.selectable.Constructor=i,t(function(){t('[data-ride="selectable"]').selectable()})}(jQuery);