/*!
 * ZUI: 数据表格 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(a){"use strict";var t="zui.datatable",e=a.zui.store,s=function(e,s){this.name=t,this.$=a(e),this.isTable="TABLE"===this.$[0].tagName,this.firstShow=!0,this.isTable?(this.$table=this.$,this.id="datatable-"+(this.$.attr("id")||a.zui.uuid())):(this.$datatable=this.$.addClass("datatable"),this.$.attr("id")?this.id=this.$.attr("id"):(this.id="datatable-"+a.zui.uuid(),this.$.attr("id",this.id))),this.getOptions(s),this.load(),this.callEvent("ready")};s.DEFAULTS={checkable:!1,checkByClickRow:!0,checkedClass:"active",checkboxName:null,selectable:!0,sortable:!1,storage:!0,fixedHeader:!1,fixedHeaderOffset:0,fixedLeftWidth:"30%",fixedRightWidth:"30%",flexHeadDrag:!0,scrollPos:"in",rowHover:!0,colHover:!0,hoverClass:"hover",colHoverClass:"col-hover",fixCellHeight:!0,mergeRows:!1,minColWidth:20,minFixedLeftWidth:200,minFixedRightWidth:200,minFlexAreaWidth:200},s.prototype.getOptions=function(t){var e=this.$;t=a.extend({},s.DEFAULTS,this.$.data(),t),t.tableClass=t.tableClass||"",t.tableClass=" "+t.tableClass+" table-datatable",a.each(["bordered","condensed","striped","condensed","fixed"],function(a,s){s="table-"+s,e.hasClass(s)&&(t.tableClass+=" "+s)}),(e.hasClass("table-hover")||t.rowHover)&&(t.tableClass+=" table-hover"),t.checkable&&a.fn.selectable||(t.selectable=!1),this.options=t},s.prototype.load=function(e){var s,l=this.options;if("function"==typeof e)e=e(this.data,this),e.keepSort=!0;else if(a.isPlainObject(e))this.data=e;else if("string"==typeof e){var d=a(e);d.length&&(this.$table=d.first(),this.$table.data(t,this),this.isTable=!0),e=null}else e=l.data;if(!e){if(!this.isTable)throw new Error("No data available!");e={cols:[],rows:[]},s=e.cols;var i,o,r,n,c,h,f=e.rows,p=this.$table;p.children("thead").children("tr:first").children("th").each(function(){o=a(this),s.push(a.extend({text:o.html(),flex:o.hasClass("flex-col"),width:"auto",cssClass:o.attr("class"),css:o.attr("style"),type:"string",ignore:o.hasClass("ignore"),sort:!o.hasClass("sort-disabled"),mergeRows:o.attr("merge-rows"),title:o.attr("title")},o.data()))}),p.children("tbody").children("tr").each(function(){r=a(this),c=a.extend({data:[],checked:!1,cssClass:r.attr("class"),css:r.attr("style"),id:r.attr("id")},r.data()),r.children("td").each(function(){if(n=a(this),h=n.attr("colspan")||1,c.data.push(a.extend({cssClass:n.attr("class"),css:n.attr("style"),text:n.html(),colSpan:h,title:n.attr("title")},n.data())),h>1)for(i=1;i<h;i++)c.data.push({empty:!0})}),f.push(c)});var b=p.children("tfoot");b.length&&(e.footer=a('<table class="table'+l.tableClass+'"></table>').append(b))}e.flexStart=-1,e.flexEnd=-1,s=e.cols,e.colsLength=s.length;for(var i=0;i<e.colsLength;++i){var g=s[i];g.flex&&(e.flexStart<0&&(e.flexStart=i),e.flexEnd=i)}0===e.flexStart&&e.flexEnd===e.colsLength&&(e.flexStart=-1,e.flexEnd=-1),e.flexArea=e.flexStart>=0,e.fixedRight=e.flexEnd>=0&&e.flexEnd<e.colsLength-1,e.fixedLeft=e.flexStart>0,e.flexStart<0&&e.flexEnd<0&&(e.fixedLeft=!0,e.flexStart=e.colsLength,e.flexEnd=e.colsLength),this.data=e,this.callEvent("afterLoad",{data:e}),this.render()},s.prototype.render=function(){var e,s,l,d,i=this,o=i.$datatable||(i.isTable?a('<div class="datatable" id="'+i.id+'"/>'):i.$datatable),r=i.options,n=i.data,c=i.data.cols,h=i.data.rows,f=r.checkable,p='<div class="datatable-rows-span datatable-span"><div class="datatable-wrapper"><table class="table"></table></div></div>',b='<div class="datatable-head-span datatable-span"><div class="datatable-wrapper"><table class="table"><thead></thead></table></div></div>';o.children(".datatable-head, .datatable-rows, .scroll-wrapper").remove(),o.toggleClass("sortable",r.sortable);var g,v,u,w=a('<div class="datatable-head"/>');for(e=a('<tr class="datatable-row datatable-row-left"/>'),l=a('<tr class="datatable-row datatable-row-right"/>'),d=a('<tr class="datatable-row datatable-row-flex"/>'),s=0;s<c.length;s++)u=c[s],g=s<n.flexStart?e:s>=n.flexStart&&s<=n.flexEnd?d:l,0===s&&f&&g.append('<th data-index="check" class="check-all check-btn"><i class="icon-check-empty"></i></th>'),u.ignore||(v=a('<th class="datatable-head-cell"/>'),v.toggleClass("sort-down","down"===u.sort).toggleClass("sort-up","up"===u.sort).toggleClass("sort-disabled",u.sort===!1),v.addClass(u.cssClass).addClass(u.colClass).html(u.text).attr({"data-index":s,"data-type":u.type,style:u.css,title:u.title}).css("width",u.width),g.append(v));var x;n.fixedLeft&&(x=a(b),x.addClass("fixed-left").find("table").addClass(r.tableClass).find("thead").append(e),w.append(x)),n.flexArea&&(x=a(b),x.addClass("flexarea").find(".datatable-wrapper").append('<div class="scrolled-shadow scrolled-in-shadow"></div><div class="scrolled-shadow scrolled-out-shadow"></div>').find("table").addClass(r.tableClass).find("thead").append(d),w.append(x)),n.fixedRight&&(x=a(b),x.addClass("fixed-right").find("table").addClass(r.tableClass).find("thead").append(l),w.append(x)),o.append(w);var C,m,k,y,$,S,A,E,T=a('<div class="datatable-rows">'),L=h.length;e=a("<tbody/>"),l=a("<tbody/>"),d=a("<tbody/>");for(var H=0;H<L;++H){for(S=h[H],"undefined"==typeof S.id&&(S.id=H),S.index=H,C=a('<tr class="datatable-row"/>'),C.addClass(S.cssClass).toggleClass(r.checkedClass,!!S.checked).attr({"data-index":H,"data-id":S.id}),m=C.clone().addClass("datatable-row-flex"),k=C.clone().addClass("datatable-row-right"),C.addClass("datatable-row-left"),E=S.data.length,s=0;s<E;++s)A=S.data[s],s>0&&A.empty||(g=s<n.flexStart?C:s>=n.flexStart&&s<=n.flexEnd?m:k,0===s&&f&&($=a('<td data-index="check" class="check-row check-btn"><i class="icon-check-empty"></i></td>'),r.checkboxName&&$.append('<input class="hide" type="checkbox" name="'+r.checkboxName+'" value="'+S.id+'">'),g.append($)),c[s].ignore||(a.isPlainObject(A)?(A.row=H,A.index=s):A={text:A,row:H,index:s},S.data[s]=A,y=a('<td class="datatable-cell"/>'),y.html(A.text).addClass(A.cssClass).addClass(c[s].colClass).attr("colspan",A.colSpan).attr({"data-row":H,"data-index":s,"data-flex":!1,"data-type":c[s].type,style:A.css,title:A.title||""}).css("width",c[s].width),g.append(y)));e.append(C),d.append(m),l.append(k)}var R;n.fixedLeft&&(R=a(p),R.addClass("fixed-left").find("table").addClass(r.tableClass).append(e),T.append(R)),n.flexArea&&(R=a(p),R.addClass("flexarea").find(".datatable-wrapper").append('<div class="scrolled-shadow scrolled-in-shadow"></div><div class="scrolled-shadow scrolled-out-shadow"></div>').find("table").addClass(r.tableClass).append(d),T.append(R)),n.fixedRight&&(R=a(p),R.addClass("fixed-right").find("table").addClass(r.tableClass).append(l),T.append(R)),o.append(T),n.flexArea&&o.append('<div class="scroll-wrapper"><div class="scroll-slide scroll-pos-'+r.scrollPos+'"><div class="bar"></div></div></div>');var z=o.children(".datatable-footer").detach();n.footer?(o.append(a('<div class="datatable-footer"/>').append(n.footer)),n.footer=null):z.length&&o.append(z),i.$datatable=o.data(t,i),i.isTable&&i.firstShow&&(i.$table.attr("data-datatable-id",this.id).hide().after(o),i.firstShow=!1),i.bindEvents(),i.refreshSize(),i.callEvent("render")},s.prototype.toggleAnimation=function(a){var t=this;void 0===a&&(a=t.$.hasClass("no-animation")),t.toggleAnimationTimer&&(clearTimeout(t.toggleAnimationTimer),t.toggleAnimationTimer=null),a?t.toggleAnimationTimer=setTimeout(function(){t.toggleAnimationTimer=null,t.$.removeClass("no-animation")},500):t.$.addClass("no-animation")},s.prototype.bindEvents=function(){var t=this,s=this.data,l=this.options,d=this.$datatable,i=t.$dataSpans=d.children(".datatable-head, .datatable-rows").find(".datatable-span"),o=t.$rowsSpans=d.children(".datatable-rows").children(".datatable-rows-span"),r=t.$headSpans=d.children(".datatable-head").children(".datatable-head-span"),n=t.$cells=i.find(".datatable-head-cell,.datatable-cell"),c=t.$dataCells=n.filter(".datatable-cell");t.$headCells=n.filter(".datatable-head-cell");var h=t.$rows=t.$rowsSpans.find(".datatable-row");if(l.rowHover){var f=l.hoverClass;o.on("mouseenter",".datatable-cell",function(){c.filter("."+f).removeClass(f),h.filter("."+f).removeClass(f),h.filter('[data-index="'+a(this).addClass(f).data("row")+'"]').addClass(f)}).on("mouseleave",".datatable-cell",function(){c.filter("."+f).removeClass(f),h.filter("."+f).removeClass(f)})}if(l.colHover){var p=l.colHoverClass;r.on("mouseenter",".datatable-head-cell",function(){n.filter("."+p).removeClass(p),n.filter('[data-index="'+a(this).data("index")+'"]').addClass(p)}).on("mouseleave",".datatable-head-cell",function(){n.filter("."+p).removeClass(p)})}if(s.flexArea){var b,g,v,u,w,x,C=d.find(".scroll-slide"),m=d.find(".datatable-span.flexarea"),k=d.find(".datatable-span.fixed-left"),y=d.find(".datatable-span.flexarea .table-datatable"),$=C.children(".bar"),S=t.id+"_scrollOffset";t.width=d.width(),d.resize(function(){t.width=d.width()});var A=function(a,t){u=Math.max(0,Math.min(b-g,a)),t||d.addClass("scrolling"),$.css("left",u),x=0-Math.floor((v-b)*u/(b-g)),y.css("left",x),d.toggleClass("scrolled-in",u>2).toggleClass("scrolled-out",u<b-g-2),l.storage&&e.pageSet(S,u)},E=function(){b=m.width(),C.css({width:b,left:k.width()}),v=0,y.first().find("tr:first").children("td,th").each(function(){v+=a(this).outerWidth()}),g=Math.floor(b*b/v),$.css("width",g),y.css("min-width",Math.max(b,v)),d.toggleClass("show-scroll-slide",v>b),w||b===g||(w=!0,A(e.pageGet(S,0),!0)),d.hasClass("size-changing")&&A(u,!0)};m.resize(E),l.storage&&E();var T={move:!1,stopPropagation:!0,drag:function(a){A($.position().left+a.smallOffset.x*(a.element.hasClass("bar")?1:-1))},finish:function(){d.removeClass("scrolling")}};a.fn.draggable?($.draggable(T),l.flexHeadDrag&&d.find(".datatable-head-span.flexarea").draggable(T)):console.error("DataTable requires draggable.js to improve UI."),C.mousedown(function(a){var t=a.pageX-C.offset().left;A(t-g/2)})}if(l.checkable){var L,H=t.id+"_checkedStatus",R=l.checkedClass,z=function(){var d=o.first().find(".datatable-row"),i=d.filter("."+R);l.checkboxName&&d.find(".check-row input:checkbox").prop("checked",!1);var n={checkedAll:d.length===i.length&&i.length>0,checks:i.map(function(){return L=a(this).data("id"),l.checkboxName&&a(this).find(".check-row input:checkbox").prop("checked",!0),L}).toArray()};t.checks=n,a.each(s.rows,function(t,e){e.checked=a.inArray(e.id,n.checks)>-1}),r.find(".check-all").toggleClass("checked",!!n.checkedAll),l.storage&&e.pageSet(H,n),t.callEvent("checksChanged",{checks:n})},O=function(t,e){var s=a(t).closest("tr");void 0===e&&(e=!s.hasClass(R)),h.filter('[data-index="'+s.data("index")+'"]').toggleClass(R,!!e)},N="click.zui.datatable.check";if(l.selectable){var W={selector:".datatable-rows .datatable-row",trigger:".datatable-rows",start:function(t){var e=a(t.target).closest(".check-row, .check-btn");if(e.length)return e.is(".check-row")&&(O(e),z()),!1},rangeFunc:function(a,t){return Math.max(a.top,t.top)<Math.min(a.top+a.height,t.top+t.height)},select:function(a){O(a.target,!0)},unselect:function(a){O(a.target,!1)},finish:function(a){z()}};a.isPlainObject(l.selectable)&&a.extend(W,l.selectable),this.$datatable.selectable(W)}else this.$rowsSpans.off(N).on(N+"row",l.checkByClickRow?"tr":".check-row",function(){O(this),z()});if(this.$datatable.off(N).on("click.zui.datatable.check",".check-all",function(){t.toggleAnimation(!1),h.toggleClass(R,a(this).toggleClass("checked").hasClass("checked")),z(),t.toggleAnimation(!0)}).on(N+".none",".check-none",function(){t.toggleAnimation(!1),h.toggleClass(R,!1),z(),t.toggleAnimation(!0)}).on(N+".inverse",".check-inverse",function(){t.toggleAnimation(!1),h.toggleClass(R),z(),t.toggleAnimation(!0)}),l.storage){var F=e.pageGet(H);F&&(r.find(".check-all").toggleClass("checked",F.checkedAll),F.checkedAll?h.addClass(R):(h.removeClass(R),a.each(F.checks,function(a,t){h.filter('[data-id="'+t+'"]').addClass(R)})),F.checks.length&&z())}}if(l.fixedHeader){var M,j,D,P=d.children(".datatable-head"),B=l.fixedHeaderOffset||a(".navbar.navbar-fixed-top").height()||0,G=function(){M=d.offset().top,D=a(window).scrollTop(),j=d.height(),d.toggleClass("head-fixed",D+B>M&&D+B<M+j),d.hasClass("head-fixed")?P.css({width:d.width(),top:B}):P.attr("style","")};a(window).scroll(G),G()}l.sortable?(r.on("click","th:not(.sort-disabled, .check-btn)",function(){d.hasClass("size-changing")||t.sortTable(a(this))}),l.storage&&t.sortTable()):l.mergeRows&&this.mergeRows()},s.prototype.mergeRows=function(){for(var t=this.$rowsSpans.find(".datatable-cell"),e=this.data.cols,s=0;s<e.length;s++){var l=e[s];if(l.mergeRows){var d=t.filter('[data-index="'+s+'"]');if(d.length>1){var i,o;d.each(function(){var t=a(this);i&&t.html()===i.html()?(o=i.attr("rowspan")||1,"number"!=typeof o&&(o=parseInt(o),isNaN(o)&&(o=1)),i.attr("rowspan",o+1).css("vertical-align","middle"),t.remove()):i=t})}}}},s.prototype.sortTable=function(t){var e=a.zui.store,s=this.options,l=this.id+"_datatableSorter",d=s.storage?e.pageGet(l):null;if(t||(t=d?this.$headCells.filter('[data-index="'+d.index+'"]').addClass("sort-"+("up"===d.type?"down":"up")):this.$headCells.filter(".sort-up, .sort-down").first()),t.length){var i,o,r,n=this.data,c=n.cols,h=n.rows,f=this.$headCells;i=!t.hasClass("sort-up"),n.keepSort&&(i=!i),n.keepSort=null,f.removeClass("sort-up sort-down"),t.addClass(i?"sort-up":"sort-down"),r=t.data("index"),a.each(c,function(a,t){a==r||"up"!==t.sort&&"down"!==t.sort?a==r&&(t.sort=i?"up":"down",o=t.type):t.sort=!0});var p,b,g,v=this.$dataCells.filter('[data-index="'+r+'"]');h.sort(function(a,t){return a=a.data[r],t=t.data[r],p=v.filter('[data-row="'+a.row+'"]').text(),b=v.filter('[data-row="'+t.row+'"]').text(),"number"===o?(p=parseFloat(p),b=parseFloat(b)):"date"===o?(p=Date.parse(p),b=Date.parse(b)):(p=p.toLowerCase(),b=b.toLowerCase()),g=p<b?1:p>b?-1:0,i&&(g*=-1),g});var u,w,x,C=this.$rows,m=[];a.each(h,function(t,e){u=C.filter('[data-index="'+e.index+'"]'),u.each(function(t){x=a(this),w=m[t],w?w.after(x):x.parent().prepend(x),m[t]=x})}),d={index:r,type:i?"up":"down"},s.storage&&e.pageSet(l,d),this.callEvent("sort",{sorter:d})}},s.prototype.refreshSize=function(){var t,e=this.$datatable,s=this.options,l=this.data.rows;if(e.find(".datatable-span.fixed-left").css("width",s.fixedLeftWidth),e.find(".datatable-span.fixed-right").css("width",s.fixedRightWidth),s.fixCellHeight){var d=function(t){var e,s,l=0;return t.css("height","auto"),t.each(function(){e=a(this),s=e.attr("rowspan"),s&&1!=s||(l=Math.max(l,e.outerHeight()))}),l},i=this.$dataCells,o=this.$headCells,r=d(o);o.css("min-height",r).css("height",r);var n;for(t=0;t<l.length;++t){n=i.filter('[data-row="'+t+'"]');var c=d(n);n.css("min-height",c).css("height",c)}}},s.prototype.callEvent=function(a,t){var e=this.$.callEvent(a+"."+this.name,t,this).result;return!(void 0!==e&&!e)},a.fn.datatable=function(e,l){return this.each(function(){var d=a(this),i=d.data(t),o="object"==typeof e&&e;i||d.data(t,i=new s(this,o)),"string"==typeof e&&("load"!==e||!a.isPlainObject(l)||void 0!==l.keepSort&&null!==l.keepSort||(l.keepSort=!0),i[e](l))})},a.fn.datatable.Constructor=s}(jQuery);