/*!
 * ZUI: 颜色选择器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t){"use strict";var o="zui.colorPicker",i='<div class="colorpicker"><button type="button" class="btn dropdown-toggle" data-toggle="dropdown"><span class="cp-title"></span><i class="ic"></i></button><ul class="dropdown-menu clearfix"></ul></div>',e={zh_cn:{errorTip:"不是有效的颜色值"},zh_tw:{errorTip:"不是有效的顏色值"},en:{errorTip:"Not a valid color value"}},r=function(i,e){this.name=o,this.$=t(i),this.getOptions(e),this.init()};r.prototype.init=function(){var o=this,e=o.options,r=o.$,a=r.parent(),n=!1;a.hasClass("colorpicker")?o.$picker=a:(o.$picker=t(e.template||i),n=!0),o.$picker.addClass(e.wrapper).find(".cp-title").toggle(void 0!==e.title).text(e.title),o.$menu=o.$picker.find(".dropdown-menu").toggleClass("pull-right",e.pullMenuRight),o.$btn=o.$picker.find(".btn.dropdown-toggle"),o.$btn.find(".ic").addClass("icon-"+e.icon),e.btnTip&&o.$picker.attr("data-toggle","tooltip").tooltip({title:e.btnTip,placement:e.tooltip,container:"body"}),r.attr("data-provide",null),n&&r.after(o.$picker),o.colors={},t.each(e.colors,function(i,e){if(t.zui.Color.isColor(e)){var r=new t.zui.Color(e);o.colors[r.toCssStr()]=r}}),o.updateColors(),o.$picker.on("click",".cp-tile",function(){o.setValue(t(this).data("color"))});var l=function(){var i=r.val(),a=t.zui.Color.isColor(i);r.parent().toggleClass("has-error",!(a||e.optional&&""===i)),a?o.setValue(i,!0):e.optional&&""===i?r.tooltip("hide"):r.is(":focus")||r.tooltip("show",e.errorTip)};r.is("input:not([type=hidden])")?(e.tooltip&&r.attr("data-toggle","tooltip").tooltip({trigger:"manual",placement:e.tooltip,tipClass:"tooltip-danger",container:"body"}),r.on("keyup paste input change",l)):r.appendTo(o.$picker),l()},r.prototype.addColor=function(o){o instanceof t.zui.Color||(o=new t.zui.Color(o));var i=o.toCssStr(),e=this.options;this.colors[i]||(this.colors[i]=o);var r=t('<a href="###" class="cp-tile"></a>',{titile:o}).data("color",o).css({color:o.contrast().toCssStr(),background:i,"border-color":o.luma()>.43?"#ccc":"transparent"}).attr("data-color",i);this.$menu.append(t("<li/>").css({width:e.tileSize,height:e.tileSize}).append(r)),e.optional&&this.$menu.find(".cp-tile.empty").parent().detach().appendTo(this.$menu)},r.prototype.updateColors=function(o){var i=this.$menu,e=this.options,o=o||this.colors,r=this,a=0;if(i.children("li:not(.heading)").remove(),t.each(o,function(t,o){r.addColor(o),a++}),e.optional){var n=t('<li><a class="cp-tile empty" href="###"></a></li>').css({width:e.tileSize,height:e.tileSize});this.$menu.append(n),a++}i.css("width",Math.min(a,e.lineCount)*e.tileSize+6)},r.prototype.setValue=function(o,i){var e=this,r=e.options,a=e.$btn,n="";e.$menu.find(".cp-tile.active").removeClass("active");var l=r.updateBtn;if("auto"===l){var c=a.find(".color-bar");l=!c.length||function(t){c.css("background",t||"")}}if(o){var p=new t.zui.Color(o);n=p.toCssStr().toLowerCase(),l&&("function"==typeof l?l(n,a,e):a.css({background:n,color:p.contrast().toCssStr(),borderColor:p.luma()>.43?"#ccc":n})),e.colors[n]||e.addColor(p),i||e.$.val().toLowerCase()===n||e.$.val(n).trigger("change"),e.$menu.find('.cp-tile[data-color="'+n+'"]').addClass("active"),e.$.tooltip("hide"),e.$.trigger("colorchange",p)}else l&&("function"==typeof l?l(null,a,e):a.attr("style",null)),i||""===e.$.val()||e.$.val(n).trigger("change"),r.optional&&e.$.tooltip("hide"),e.$menu.find(".cp-tile.empty").addClass("active"),e.$.trigger("colorchange",null);r.updateBorder&&t(r.updateBorder).css("border-color",n),r.updateBackground&&t(r.updateBackground).css("background-color",n),r.updateColor&&t(r.updateColor).css("color",n),r.updateText&&t(r.updateText).text(n)},r.prototype.getOptions=function(i){var a=t.extend({},r.DEFAULTS,this.$.data(),i);"string"==typeof a.colors&&(a.colors=a.colors.split(","));var n=a.lang||t.zui.clientLang(),l=this.lang=t.zui.getLangData?t.zui.getLangData(o,n,e):e[n]||e.en;a.errorTip||(a.errorTip=l.errorTip),t.fn.tooltip||(a.btnTip=!1),this.options=a},r.DEFAULTS={colors:["#00BCD4","#388E3C","#3280fc","#3F51B5","#9C27B0","#795548","#F57C00","#F44336","#E91E63"],pullMenuRight:!0,wrapper:"btn-wrapper",tileSize:30,lineCount:5,optional:!0,tooltip:"top",icon:"caret-down",updateBtn:"auto"},r.LANG=e,t.fn.colorPicker=function(o){return this.each(function(){var i=t(this),e=i.data(name),a="object"==typeof o&&o;e||i.data(name,e=new r(this,a)),"string"==typeof o&&e[o]()})},t.fn.colorPicker.Constructor=r,t(function(){t('[data-provide="colorpicker"]').colorPicker()})}(jQuery);