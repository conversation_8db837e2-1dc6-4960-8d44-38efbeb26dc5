/*!
 * ZUI: 看板 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */

.board-item {
  padding: 6px 10px;
  margin-bottom: 5px;
  background: #fff;
  border: 1px solid #ddd;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, .05);
          box-shadow: 0 1px 0 rgba(0, 0, 0, .05);
  -webkit-transition: all .4s cubic-bezier(.175, .885, .32, 1);
       -o-transition: all .4s cubic-bezier(.175, .885, .32, 1);
          transition: all .4s cubic-bezier(.175, .885, .32, 1);
  }
.board-item:hover {
  -webkit-box-shadow: 0 1px 1 rgba(0, 0, 0, .1);
          box-shadow: 0 1px 1 rgba(0, 0, 0, .1);
  }
.board-item.board-item-empty {
  display: none;
  color: #808080;
  border-style: dashed;
  }
.board-item.board-item-shadow {
  display: none;
  padding: 0;
  background: #ddd;
  border: none;
  border-color: #ddd;
  -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .1);
          box-shadow: inset 0 0 4px rgba(0, 0, 0, .1);
  -webkit-transition: all .4s cubic-bezier(.175, .885, .32, 1);
       -o-transition: all .4s cubic-bezier(.175, .885, .32, 1);
          transition: all .4s cubic-bezier(.175, .885, .32, 1);
  }
.board-item.drag-shadow {
  width: 250px;
  cursor: move;
  background-color: #fff;
  border-color: #c4c4c4;
  -webkit-box-shadow: 1px 1px 15px rgba(0, 0, 0, .25);
          box-shadow: 1px 1px 15px rgba(0, 0, 0, .25);
  opacity: .9;
  }
.board-item.drag-from {
  background-color: #ebf2f9;
  }
.board-list .board-item:last-child {
  margin-bottom: 0;
  }
.board {
  float: left;
  width: 250px;
  margin-right: 10px;
  }
.board.drop-in-empty .board-item-empty {
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  border: transparent;
  }
.board:last-child {
  margin-right: 0;
  }
.board > .panel-body {
  padding: 5px;
  background: #f1f1f1;
  }
.boards:before,
.boards:after {
  display: table;
  content: " ";
  }
.boards:after {
  clear: both;
  }
.boards.dragging .board.drop-in {
  border-color: #c4c4c4;
  -webkit-box-shadow: 1px 1px 15px rgba(0, 0, 0, .25);
          box-shadow: 1px 1px 15px rgba(0, 0, 0, .25);
  }
.boards.dragging .board.drop-in .board-item-shadow {
  display: block;
  }
.boards.dragging .board .board-item.board-item-empty {
  display: block;
  }
.boards.dragging .board-item.disable-drop {
  display: none;
  }
.boards.drop-in .board-item.drag-from {
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  border: transparent;
  }
