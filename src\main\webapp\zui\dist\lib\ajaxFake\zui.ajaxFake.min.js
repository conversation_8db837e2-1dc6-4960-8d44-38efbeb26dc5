/*!
 * ZUI: Ajax 响应模拟工具 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(e){"use strict";var a=[],l=e.ajax,r=function(l){for(var r=a.length-1;r>=0;--r){var t=a[r];if(t.urlMatch instanceof RegExp){if(t.urlMatch.test(l))return e.extend(!0,{url:l},t)}else if("function"==typeof t.urlMatch){if(t.urlMatch(l))return e.extend(!0,{url:l},t)}else if(0===l.indexOf(t.urlMatch))return e.extend(!0,{url:l},t)}},t=function(a){var t=a.url,n=r(t);if(n){var o=function(l,r){if(!n.aborted){var t=a.dataType;"json"===t?"string"==typeof l&&(l=e.parseJSON(l)):"string"!=typeof l&&(l=JSON.stringify(l)),a.success&&a.success(l,r),n.doneCallback&&n.doneCallback(l,r),a.complete&&a.complete(null,r),n.alwaysCallback&&n.alwaysCallback(l,r)}},c=function(e,l){n.aborted||(a.error&&a.error(null,e,l),n.failCallback&&n.failCallback(e,l),a.complete&&a.complete(null,e),n.alwaysCallback&&n.alwaysCallback(e,l))};a.beforeSend&&a.beforeSend(null,a);var u=function(){if(!n.aborted){var e="function"==typeof n.route?n.route.call(n,a,o,c):n.route;return e!==!0&&(e===!1?c(500):o(e,200)),console.groupCollapsed("%cFakeAjax %c"+t+"%c "+(e===!1?"Error":"Success"),"color: lightblue","text-decoration: underline",e===!1?"color: red":"color: green"),console.log("response",e),console.log("ajax options",a),console.groupEnd(),e}};return setTimeout(u,n.delay||0),n}return l.apply(null,arguments)},n=function(r,n,o){e.ajax_origin||(e.ajax_origin=l,e.ajax=t);var c={done:function(e){return this.doneCallback=e,this},fail:function(e){return this.failCallback=e,this},always:function(e){return this.alwaysCallback=e,this},abort:function(){this.aborted=!0}};e.isPlainObject(r)?e.extend(c,r):(c.urlMatch=r,c.route=n,e.isPlainObject(o)?e.extend(c,o):"number"==typeof o&&(c.delay=o)),a.push(c)};e.fakeServer=n}(jQuery);