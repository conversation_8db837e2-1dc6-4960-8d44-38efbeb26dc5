package com.ruoyi.servlet;

import com.ruoyi.dao.UserDao;
import com.ruoyi.dao.impl.UserDaoImpl;
import com.ruoyi.entity.User;
import com.ruoyi.utils.JsonUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@WebServlet("/login")
public class HelloServlet extends HttpServlet {


    @Override

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String action = request.getParameter("action");
        if ("login".equals(action)) {
            try {
                handleLogin(request, response);
            } catch (SQLException | ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        } else if ("logout".equals(action)) {
            handleLogout(request, response);
        } else if ("checkStatus".equals(action)) {
            handleCheckStatus(request, response);
        }
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    private void handleLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
            HttpSession session = request.getSession();
            if (session != null) {
                session.invalidate();
                response.getWriter().write("注销成功");
            } else {
                response.getWriter().write("未登录");
        }
    }

    private void handleCheckStatus(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        HttpSession session = request.getSession(false);
        
        if (session != null && session.getAttribute("username") != null) {
            // 已登录，返回用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("username", session.getAttribute("username"));
            userInfo.put("userId", session.getAttribute("userId"));
            response.getWriter().write(JsonUtil.toJson(userInfo));
        } else {
            // 未登录
            response.getWriter().write("{}");
        }
    }

    private void handleLogin(HttpServletRequest request, HttpServletResponse response) throws IOException, SQLException, ClassNotFoundException {
        UserDao dao=new UserDaoImpl();
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        System.out.println("Login attempt: username=" + username + ", password=" + password);
        User userByUsernameAndPwd = dao.getUserByUsernameAndPwd(username, password);
        if (userByUsernameAndPwd.getPwd().equals(password)) {
            HttpSession session = request.getSession();
            session.setAttribute("username", username);
            session.setAttribute("userId", userByUsernameAndPwd.getUser_id());
            session.setAttribute("user", userByUsernameAndPwd);
            session.setMaxInactiveInterval(30*24*60*60);
            response.getWriter().write("登录成功");
            System.out.println("Login successful for user: " + username);
        } else {
            response.getWriter().write("登录失败");
            System.out.println("Login failed for user: " + username);
        }
    }


}