<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="zui/dist/css/zui.min.css" />
    <script src="zui/dist/lib/jquery/jquery-3.4.1.min.js"></script>
    <script src="zui/dist/js/zui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>
    <script src="common.js"></script>
    <title>我的快递列表</title>
</head>

<body>
    <nav class="navbar navbar-inverse" role="navigation">
        <div class="container-fluid">
            <!-- 导航头部 -->
            <div class="navbar-header">
                <!-- 移动设备上的导航切换按钮 -->
                <button type="button" class="navbar-toggle" data-toggle="collapse"
                    data-target=".navbar-collapse-example">
                    <span class="sr-only">切换导航</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <!-- 品牌名称或logo -->
                <a class="navbar-brand" href="index.html">顺丰物流</a>
            </div>
            <!-- 导航项目 -->
            <div class="collapse navbar-collapse" id="navbar-collapse-example">
                <!-- 左侧的导航项目 -->
                <ul class="nav navbar-nav navibar-left">
                    <li><a href="index.html">首页</a></li>
                    <li class="active"><a href="parcel_list.html">我的快递</a></li>
                </ul>
                <!-- 右侧的导航项目 -->
                <ul class="nav navbar-nav navbar-right">
                    <li><a href="OrderServlet?action=send" id="send-btn">我要寄件</a></li>
                </ul>
                <ul class="nav navbar-nav navbar-right" id="loginInfo">
                    <li><a href="index.html#login">登录/注册</a></li>
                </ul>
                <ul class="nav navbar-nav navbar-right" id="welcomeInfo">
                    <li id="userInfo"></li>
                </ul>
            </div><!-- END .navbar-collapse -->
        </div>
    </nav>

    <div class="container">
        <h2 class="text-center">我的快递列表</h2>
        <table class="table table-striped table-hover" id="expressTable">
            <thead>
                <tr>
                    <th>订单号</th>
                    <th>收件人</th>
                    <th>订单类型</th>
                    <th>订单状态</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody id="tbody">
                <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>
</body>

</html>
<script>
    $(function () {
        // 初始化通用功能（登录状态检查和登出功能）
        initCommonFeatures();
        
        // 只有登录后才能查看快递列表
        checkLoginStatus().then(function(response) {
            if (response && response.username) {
                loadExpressData();
            } else {
                $('#tbody').html(`
                    <tr>
                        <td colspan="5" class="text-center">请先登录后查看您的快递列表</td>
                    </tr>
                `);
                
                // 3秒后跳转到登录页面
                setTimeout(function() {
                    window.location.href = "index.html#login";
                }, 3000);
            }
        });
    });

    function loadExpressData() {
        $.post('parcel/list', {}, function (data) {
            if (data && data.length > 0) {
                let html = '';
                data.forEach(item => {
                    html += `
                    <tr>
                        <td>${item.parcelNo}</td>
                        <td>${item.receiverName}</td>
                        <td>${item.parcelType}</td>
                        <td><span class="${getStatusClass(item.status)}">${getStatusStr(item.status)}</span></td>
                        <td>${dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')}</td>
                    </tr>
                    `;
                });
                $('#tbody').html(html);
            } else {
                $('#tbody').html(`
                    <tr>
                        <td colspan="5" class="text-center">暂无快递记录</td>
                    </tr>
                `);
            }
        }, 'json').fail(function() {
            $('#tbody').html(`
                <tr>
                    <td colspan="5" class="text-center">获取数据失败，请稍后再试</td>
                </tr>
            `);
        });
    }

    function getStatusStr(status) {
        switch (status) {
            case 0:
                return '待处理';
            case 1:
                return '处理中';
            case 2:
                return '处理完成';
            default:
                return '未知';
        }
    }

    function getStatusClass(status) {
        switch (status) {
            case 0:
                return 'label label-success';
            case 1:
                return 'label label-warning';
            case 2:
                return 'label label-danger';
            default:
                return 'label label-default';
        }
    }
</script>