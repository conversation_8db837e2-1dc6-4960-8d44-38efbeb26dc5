/*!
 * ZUI: 文件上传 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */.uploader{position:relative;margin-bottom:20px}.uploader-btn-hidden{position:absolute;top:-1px;left:-1px;width:1px;height:1px;opacity:0}.file-dragable{position:relative}[data-drop-placeholder]:before{position:absolute;top:0;left:0;z-index:100;display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;height:100%;font-size:16px;text-align:center;pointer-events:none;content:attr(data-drop-placeholder);background-color:rgba(255,240,213,.5);filter:alpha(opacity=0);border:2px dashed #f1a325;opacity:0;-webkit-transition:all .2s;-o-transition:all .2s;transition:all .2s;-webkit-transform:scale(.95);-ms-transform:scale(.95);-o-transform:scale(.95);transform:scale(.95);-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.ie [data-drop-placeholder]:before{display:none!important}.file-dragable[data-drop-placeholder]:before{filter:alpha(opacity=100);opacity:1;-webkit-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}.ie .file-dragable[data-drop-placeholder]:before{display:block!important;display:-webkit-box!important;display:-webkit-flex!important;display:-ms-flexbox!important;display:flex!important}.file-drag-enter[data-drop-placeholder]:before{background-color:#fff0d5;border-style:solid}.ie .file-drag-enter.file-dragable>.uploader-message{pointer-events:none}.file-list,.uploader-files{position:relative;min-height:32px;margin-bottom:10px;border:1px solid #ddd}.file-list[data-drag-placeholder]:before,.uploader-files[data-drag-placeholder]:before{position:absolute;top:50%;right:0;left:0;display:block;margin-top:-15px;line-height:32px;color:#ddd;text-align:center;pointer-events:none;content:attr(data-drag-placeholder);-webkit-transition:all .4s;-o-transition:all .4s;transition:all .4s}.file-list[data-drag-placeholder]:hover:before,.uploader-files[data-drag-placeholder]:hover:before{color:grey}.file-list .file-icon,.uploader-files .file-icon{position:relative;width:32px;height:32px;line-height:32px;text-align:center;filter:alpha(opacity=70);opacity:.7;-webkit-transition:opacity .4s;-o-transition:opacity .4s;transition:opacity .4s}.file-list .file-icon-image,.uploader-files .file-icon-image{position:absolute;top:5px;right:5px;bottom:5px;left:5px;background-color:#fff;background-repeat:no-repeat;background-position:center;-webkit-background-size:cover;background-size:cover;border:1px solid #ddd}.file-list .file-name,.uploader-files .file-name{text-decoration:none;-webkit-transition:all .2s;-o-transition:all .2s;transition:all .2s}.file-list .file-name[contenteditable],.uploader-files .file-name[contenteditable]{padding:0 5px;background-color:#fff;outline:1px solid #3280fc;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #97befd;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #97befd}.file-list .file-name,.file-list .file-size,.uploader-files .file-name,.uploader-files .file-size{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.file-list .file-renaming .file-name[contenteditable],.uploader-files .file-renaming .file-name[contenteditable]{text-overflow:initial}.file-list .file:hover .file-name,.uploader-files .file:hover .file-name{color:#3280fc}.file-list .file:hover .file-icon,.uploader-files .file:hover .file-icon{opacity:1}.file-list .file-status,.uploader-files .file-status{display:inline-block;line-height:20px;text-align:right}.file-list .file-status:hover,.uploader-files .file-status:hover{background-color:rgba(0,0,0,.07)}.file-list .file-status>.icon,.uploader-files .file-status>.icon{line-height:20px;vertical-align:middle;opacity:1;-webkit-transition:all .8s;-o-transition:all .8s;transition:all .8s;-webkit-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}.file-list .file-status>.icon:before,.uploader-files .file-status>.icon:before{content:'\e653'}.file-list .file-status>.text,.uploader-files .file-status>.text{display:inline-block;padding:0 6px;font-size:12px;line-height:20px}.file-list .file-status>.text:empty,.uploader-files .file-status>.text:empty{display:none}.file-list .file[data-status=uploading] .file-status>.icon,.uploader-files .file[data-status=uploading] .file-status>.icon{filter:alpha(opacity=0);opacity:0;-webkit-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0)}.file-list .file[data-status=uploading] .file-status>.text,.uploader-files .file[data-status=uploading] .file-status>.text{color:#fff;background-color:#3280fc;border-radius:10px}.file-list .file[data-status=queue] .file-status,.uploader-files .file[data-status=queue] .file-status{color:#f1a325}.file-list .file[data-status=queue] .file-status>.icon:before,.uploader-files .file[data-status=queue] .file-status>.icon:before{content:'\e6cd'}.file-list .file[data-status=failed] .file-status,.uploader-files .file[data-status=failed] .file-status{color:#ea644a}.file-list .file[data-status=failed] .file-status>.icon:before,.uploader-files .file[data-status=failed] .file-status>.icon:before{content:'\e66a'}.file-list .file[data-status=done] .file-status,.uploader-files .file[data-status=done] .file-status{color:#38b03f}.file-list .file .actions>.btn-download-file,.file-list .file .actions>.btn-reset-file,.file-list .file[data-status=uploading] .actions>.btn,.file-list .file[data-status=failed] .actions>.btn-rename-file,.file-list .file[data-status=done] .actions>.btn,.uploader-files .file .actions>.btn-download-file,.uploader-files .file .actions>.btn-reset-file,.uploader-files .file[data-status=uploading] .actions>.btn,.uploader-files .file[data-status=failed] .actions>.btn-rename-file,.uploader-files .file[data-status=done] .actions>.btn{display:none}.file-list .file[data-status=failed] .actions>.btn-reset-file,.file-list .file[data-status=done] .actions>.btn-download-file[href],.file-list.file-show-delete-action-on-done .file[data-status=done] .actions>.btn-delete-file,.file-list.file-show-rename-action-on-done .file[data-status=done] .actions>.btn-rename-file,.uploader-files .file[data-status=failed] .actions>.btn-reset-file,.uploader-files .file[data-status=done] .actions>.btn-download-file[href],.uploader-files.file-show-delete-action-on-done .file[data-status=done] .actions>.btn-delete-file,.uploader-files.file-show-rename-action-on-done .file[data-status=done] .actions>.btn-rename-file{display:inline-block}.file-list.file-rename-by-click [data-status=queue] .file-name:hover,.uploader-files.file-rename-by-click [data-status=queue] .file-name:hover{background-color:rgba(255,255,255,.5);-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #97befd;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #97befd}.file-list .file-progress-bar,.uploader-files .file-progress-bar{position:absolute;top:0;bottom:0;left:0;z-index:10;pointer-events:none;background-color:rgba(50,128,252,.1);filter:alpha(opacity=0);-webkit-box-shadow:inset 0 -2px #3280fc;box-shadow:inset 0 -2px #3280fc;opacity:0;-webkit-transition:width .6s ease,opacity .4s;-o-transition:width .6s ease,opacity .4s;transition:width .6s ease,opacity .4s}.file-list .file[data-status=uploading] .file-progress-bar,.uploader-files .file[data-status=uploading] .file-progress-bar{filter:alpha(opacity=100);opacity:1}.file-list .file[data-status=queue],.uploader-files .file[data-status=queue]{background-color:#fff0d5}.file-list .file[data-status=failed],.uploader-files .file[data-status=failed]{background-color:#ffe5e0}.file-list .file[data-status=done],.uploader-files .file[data-status=done]{background-color:#fff}.uploader-actions{background-color:#f1f1f1}.file-list+.uploader-actions{margin-top:-10px;border:1px solid #ddd;border-top:none}.uploader-actions .uploader-status{padding:5px 10px;line-height:20px}.uploader-message{position:absolute;top:0;right:0;left:0;z-index:1;display:none;padding:5px 10px;color:#fff;background:#3280fc;filter:alpha(opacity=95);opacity:.95}.uploader-message>.close{position:absolute;top:3px;right:10px;color:inherit;text-shadow:none;opacity:.4}.uploader-message>.close:hover{opacity:1}.uploader-message[data-type=danger]{background:#ea644a}.uploader-message[data-type=warning]{background:#f1a325}.uploader-message[data-type=info]{background:#03b8cf}.uploader-message[data-type=success]{background:#38b03f}.file-list .file{position:relative;z-index:0;background-color:#fff;-webkit-transition:background .4s;-o-transition:background .4s;transition:background .4s}.file-list .file+.file{border-top:1px solid #ddd}.file-list .file-wrapper{position:relative;z-index:2;display:table;width:100%;table-layout:fixed;-webkit-transition:background .4s;-o-transition:background .4s;transition:background .4s}.file-list .file-wrapper:hover{background-color:rgba(0,0,0,.05)}.file-list .file-wrapper>.actions,.file-list .file-wrapper>.content,.file-list .file-wrapper>.file-icon{display:table-cell;vertical-align:middle}.file-list .file-wrapper>.actions{width:150px;text-align:right}.file-list .file-wrapper>.actions>.btn{padding:5px 8px}.file-list .file-wrapper>.actions>.btn:hover{background-color:rgba(0,0,0,.07)}.file-list .file-name{display:block}.file-list .file-wrapper>.content>.file-name{float:left}.file-list .file-wrapper>.content>.file-size{float:right;margin-top:2px}.file-list .file-wrapper>.actions>.btn{border-radius:0}.file-list .file-status{padding:5px}.file-list-lg .file{min-height:50px}.file-list-lg .file-icon{width:50px;line-height:50px}.file-list-lg .file-icon .icon{position:relative;display:block;width:50px;font-size:28px;line-height:50px;text-align:center}.file-list-lg .file-icon .icon-file-o{position:relative;left:-2px}.file-list-lg .file-status{line-height:40px}.file-list-lg .file-status>.icon{font-size:20px}.file-list-lg .file[data-status=done] .file-status{padding:5px 12px}.file-list-lg .file-wrapper>.content>.file-name{float:none;line-height:20px}.file-list-lg .file-wrapper>.content>.file-size{float:none;line-height:14px}.file-list-lg .file-wrapper>.actions>.btn{padding:14px 8px}.file-list-lg .file-renaming .file-name[contenteditable]{font-size:14px;line-height:34px}.file-list-lg .file-renaming .file-wrapper>.content>.file-size{display:none}.file-list-grid{margin-right:-8px;margin-left:-8px;border:none}.file-list-grid:after,.file-list-grid:before{display:table;content:" "}.file-list-grid:after{clear:both}.file-list-grid .file{display:block;float:left;width:120px;height:120px;margin:8px 8px 35px 8px;border:1px solid #ddd;border-radius:4px}.file-list-grid .file .file-icon{display:block;width:118px;height:118px;overflow:hidden}.file-list-grid .file-icon>.icon{font-size:70px;line-height:118px}.file-list-grid .file-icon-image{top:-1px;right:-1px;bottom:-1px;left:-1px;border:none}.file-list-grid .file-wrapper{position:absolute;top:0;right:0;left:0;display:block;width:auto}.file-list-grid .file-wrapper>.content{position:absolute;right:-1px;bottom:-24px;left:-1px;display:block;text-align:center;-webkit-transition:all .2s;-o-transition:all .2s;transition:all .2s}.file-list-grid .file-wrapper>.content>.file-name{position:relative;z-index:5;float:none;padding:4px 0;line-height:16px;border:1px solid transparent}.file-list-grid .file-wrapper>.content>.file-size{position:absolute;top:-24px;left:4px;display:block;padding:0 5px;line-height:18px;color:#fff;background-color:grey;background-color:rgba(0,0,0,.5);filter:alpha(opacity=0);border-radius:9px;opacity:0;-webkit-transition:opacity .4s;-o-transition:opacity .4s;transition:opacity .4s}.file-list-grid .file-renaming .file-wrapper>.content>.file-name,.file-list-grid .file-wrapper>.content:hover>.file-name{text-overflow:initial;word-break:break-all;white-space:normal;background-color:#fff;border-color:#ddd;-webkit-box-shadow:none;box-shadow:none}.file-list-grid .file-renaming .file-wrapper>.content>.file-name{padding:4px;text-align:left}.file-list-grid .file:hover .file-wrapper>.content>.file-size,.file-list-grid .file[data-status=uploading] .file-wrapper>.content>.file-size{filter:alpha(opacity=100);opacity:1}.file-list-grid .file-wrapper>.actions{position:absolute;top:0;right:0;left:0;display:block;width:118px}.file-list-grid .file-wrapper:hover>.actions{background:rgba(255,255,255,.85)}.file-list-grid .file-wrapper>.actions>.file-status{position:absolute;top:0;left:0;height:28px;padding:4px 5px}.file-list-grid .file-wrapper>.actions>.file-status>.icon{position:relative;top:-1px;display:inline-block;font-size:21px;text-shadow:-1px -1px 0 #fff,1px -1px 0 #fff,-1px 1px 0 #fff,1px 1px 0 #fff}.file-list-grid .file-wrapper>.actions>.file-status>.text{padding:0}.file-list-grid .file[data-status=failed] .file-wrapper>.actions>.file-status>.icon{font-size:14px;text-shadow:none}.file-list-grid .file[data-status=uploading] .file-wrapper>.actions>.file-status>.text{position:absolute;top:4px;left:4px;padding:0 8px}.file-list-grid .file[data-status=failed] .file-wrapper>.actions>.file-status{top:4px;left:4px;height:20px;padding:0 8px;color:#fff;background-color:#ea644a;border-radius:10px}.file-list-grid .file-wrapper>.actions>.btn{padding:3px 6px;filter:alpha(opacity=0);opacity:0}.file-list-grid .file-wrapper:hover>.actions>.btn{filter:alpha(opacity=100);opacity:1}.file-list-grid .file-progress-bar{-webkit-box-shadow:inset 0 -4px #3280fc;box-shadow:inset 0 -4px #3280fc}.file-list-grid+.uploader-actions{border:none}