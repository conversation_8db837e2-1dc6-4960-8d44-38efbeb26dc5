<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="zui/dist/css/zui.min.css"/>
    <script src="zui/dist/lib/jquery/jquery-3.4.1.min.js"></script>
    <script src="zui/dist/js/zui.min.js"></script>
    <script src="common.js"></script>
    <title>物流网页首页</title>
</head>

<body>
<nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
    <div class="container-fluid">
        <!-- 导航头部 -->
        <div class="navbar-header">
            <!-- 移动设备上的导航切换按钮 -->
            <button type="button" class="navbar-toggle" data-toggle="collapse"
                    data-target=".navbar-collapse-example">
                <span class="sr-only">切换导航</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!-- 品牌名称或logo -->
            <a class="navbar-brand" href="#">顺丰物流</a>
        </div>
        <!-- 导航项目 -->
        <div class="collapse navbar-collapse" id="navbar-collapse-example">
            <!-- 左侧的导航项目 -->
            <ul class="nav navbar-nav navibar-left">
                <li class="active"><a href="your/nice/url">项目</a></li>
            </ul>
            
            <!-- 快递追踪表单 -->
            <form class="navbar-form navbar-left" role="search">
                <div class="form-group">
                    <input type="text" class="form-control" id="parcelNo" placeholder="请输入快递单号">
                </div>
                <button type="button" id="track-btn" class="btn btn-default">查询</button>
            </form>
            
            <!-- 右侧的导航项目 -->
            <ul class="nav navbar-nav navbar-right">
                <li><a href="OrderServlet?action=send" id="send-btn">我要寄件</a></li>
                <li id="my-parcel-btn"><a href="parcel_list.html">我的快递</a></li>
            </ul>
            <ul class="nav navbar-nav navbar-right" id="loginInfo">
                <li><a href="#login" data-toggle="modal">登录/注册</a></li>
            </ul>
            <ul class="nav navbar-nav navbar-right" id="welcomeInfo">
                <li id="userInfo"></li>
                <li><a href="#" id="logout-btn">退出</a></li>
            </ul>
        </div><!-- END .navbar-collapse -->
    </div>
</nav>

<!-- 对话框HTML -->
<div class="modal fade" id="login">
    <div class="modal-dialog">
        <div class="modal-content">
            <ul class="nav nav-tabs">
                <li><a data-tab href="#registerTab">注册</a></li>
                <li class="active"><a data-tab href="#logintab">登录</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="logintab">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span
                                aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
                        <h4 class="modal-title text-center">用户登录</h4>
                    </div>
                    <form class="form-horizontal" id="loginForm">
                        <div class="form-group">
                            <label for="username" class="col-sm-2">账号</label>
                            <div class="col-md-10 col-sm-10">
                                <input type="text" class="form-control" id="username" required
                                       placeholder="电子邮件/手机号/用户名">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="password" class="col-sm-2">密码</label>
                            <div class="col-md-10 col-sm-10">
                                <input type="password" required class="form-control" id="password" placeholder="密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox"> 记住我
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-4 text-center">
                                <button type="submit" id="login-btn" class="btn btn-primary btn-lg btn-block">登录
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="tab-pane" id="registerTab">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span
                                aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
                        <h4 class="modal-title text-center">用户注册</h4>
                    </div>
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label for="register-username" class="col-sm-2">账号</label>
                            <div class="col-md-10 col-sm-10">
                                <input type="text" id="register-username" class="form-control"
                                       placeholder="电子邮件/手机号/用户名">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="register-password" class="col-sm-2">密码</label>
                            <div class="col-md-10 col-sm-10">
                                <input type="password" id="register-password" class="form-control" placeholder="密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="register-re-password" class="col-sm-2">确认密码</label>
                            <div class="col-md-10 col-sm-10">
                                <input type="password" class="form-control" id="register-re-password"
                                       placeholder="请再次输入密码">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-4 text-center">
                                <button type="button" id="register-button" class="btn btn-primary btn-lg btn-block">
                                    注册
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="myNiceCarousel" class="carousel slide" data-ride="carousel">
    <!-- 圆点指示器 -->
    <ol class="carousel-indicators">
        <li data-target="#myNiceCarousel" data-slide-to="0" class="active"></li>
        <li data-target="#myNiceCarousel" data-slide-to="1"></li>
        <li data-target="#myNiceCarousel" data-slide-to="2"></li>
    </ol>

    <!-- 轮播项目 -->
    <div class="carousel-inner">
        <div class="item active">
            <img alt="First slide" src="https://openzui.com/1/docs/img/slide1.jpg">
            <div class="carousel-caption">
                <h3>我是第一张幻灯片</h3>
                <p>:)</p>
            </div>
        </div>
        <div class="item">
            <img alt="Second slide" src="https://openzui.com/1/docs/img/slide2.jpg">
            <div class="carousel-caption">
                <h3>我是第二张幻灯片</h3>
                <p>0.0</p>
            </div>
        </div>
        <div class="item">
            <img alt="Third slide" src="https://openzui.com/1/docs/img/slide3.jpg">
            <div class="carousel-caption">
                <h3>我是第三张幻灯片</h3>
                <p>最后一张咯~</p>
            </div>
        </div>
    </div>

    <!-- 项目切换按钮 -->
    <a class="left carousel-control" href="#myNiceCarousel" data-slide="prev">
        <span class="icon icon-chevron-left"></span>
    </a>
    <a class="right carousel-control" href="#myNiceCarousel" data-slide="next">
        <span class="icon icon-chevron-right"></span>
    </a>
</div>
<section class="container">
    <h2 class="text-center" style="margin-bottom: 40px;">我们的服务</h2>
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <a class="card" href="###">
                <i class="icon icon-star icon-4x col-md-offset-5 "></i>
                <div class="card-heading text-center"><strong>国内快递</strong></div>
                <div class="card-content text-center">国内次日到</div>
            </a>
        </div>
        <div class="col-md-3 col-sm-6">
            <a class="card" href="###">
                <i class="icon icon-star icon-4x col-md-offset-5"></i>
                <div class="card-heading text-center"><strong>国内快递</strong></div>
                <div class="card-content text-center">国内次日到</div>
            </a>
        </div>
        <div class="col-md-3 col-sm-6">
            <a class="card" href="###">
                <i class="icon icon-star icon-4x col-md-offset-5"></i>
                <div class="card-heading text-center"><strong>国内快递</strong></div>
                <div class="card-content text-center">国内次日到</div>
            </a>
        </div>
        <div class="col-md-3 col-sm-6">
            <a class="card" href="###">
                <i class="icon icon-star icon-4x col-md-offset-5"></i>
                <div class="card-heading text-center"><strong>国内快递</strong></div>
                <div class="card-content text-center">国内次日到</div>
            </a>
        </div>
    </div>
</section>
<footer style="background-color: black;color: white; padding:30px 0 ;">
    <div class="container">
        <div class="row">
            <div class="col-md-4 ">
                <h4>联系我们</h4>
                <p><i class="icon-phone">10086</i></p>
                <p><i class="icon-envelope"><EMAIL></i></p>
            </div>
            <div class="col-md-4 ">
                <h4>快速链接</h4>
                <ul class="list-unstyled">
                    <li><a href="#">物流追踪</a></li>
                    <li><a href="#">网络查询</a></li>
                    <li><a href="#">价格自选</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <span>关注我们</span>
                <div class="row">
                    <button class="icon-wechat" title="微信"></button>
                    <button class="icon-weibo" title="微博"></button>
                </div>
            </div>
        </div>
        <hr/>
        <div class="text-center">2025.7.11</div>
    </div>
</footer>
</body>
</html>
<script>
    $(function () {
        // 初始化通用功能（登录状态检查和登出功能）
        initCommonFeatures();
        
        // 登录表单提交处理
        $('#loginForm').on('submit', function (e) {
            e.preventDefault();//
            $.post("login", {
                action: 'login',
                username: $('#username').val(),
                password: $('#password').val()
            }, function (res) {
                if (res === "登录成功") { // 使用精确匹配
                    localStorage.setItem("username", $('#username').val())
                    $('#login').modal('hide'); // 关闭登录模态框
                    // 重新检查登录状态
                    checkLoginStatus();
                    var myMessager = new $.zui.Messager({type: 'success'});
                    myMessager.show('登录成功');
                } else {
                    var myMessager = new $.zui.Messager({type: 'danger'});
                    myMessager.show('登录失败，请检查用户名和密码');
                }
            }, "text") // 保持使用text数据类型
        });

        // 登出按钮点击处理
        $('#logout-btn').click(function (e) {
            $.post("login", {
                action: 'logout'
            }, function (res) {
                localStorage.removeItem("username");
                window.location.href="index.html"
                // 显示退出成功提示
                var myMessager = new $.zui.Messager({type: 'success'});
                myMessager.show('退出成功');
            }, "text")
        });

        // 注册按钮点击处理
        $('#register-button').click(function () {
            if ($('#register-password').val() !== $('#register-re-password').val()) {
                var myMessager = new $.zui.Messager({type: 'danger'});
                myMessager.show('密码不一致');
                return;
            }
            $.post("register", {
                username: $('#register-username').val(),
                password: $('#register-password').val(),
            }, function (res) {
                if (res === "注册成功") {
                    var myMessagerq = new $.zui.Messager({type: 'success'});
                    myMessagerq.show('注册成功，请登录');

                    $('#login').modal('hide');
                } else {
                    var myMessagere = new $.zui.Messager({type: 'danger'});
                    myMessagere.show('注册失败，请检查用户名和密码');
                }
            })
        });

        // 寄件按钮点击处理
        $('#send-btn').click(function (e) {
            e.preventDefault()
            checkLoginStatus().then(function(response) {
                if (response && response.username) {
                    window.location.href = "send.html";
                } else {
                    var myMessagerr = new $.zui.Messager({type: 'warning'});
                    myMessagerr.show('请先登录');
                    $('#login').modal('show');
                }
            });
        });

        $('.carousel').carousel()
    })
</script>