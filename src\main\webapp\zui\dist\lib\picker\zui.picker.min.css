/*!
 * ZUI: 下拉选择器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */.picker{min-height:32px;background-color:#eee}.picker:not(.picker-ready)>*{display:none}.picker-selections{position:relative;min-height:32px;color:#222;background-color:#fff;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);box-shadow:inset 0 1px 1px rgba(0,0,0,.075)}.picker-focus .picker-selections{border-color:#145ccd;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(20,92,205,.6);box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(20,92,205,.6)}.picker-multi .picker-selections{padding:3px}.picker-multi.picker-focus .picker-selections:before{position:absolute;right:0;bottom:8px;display:block;width:24px;height:14px;font-family:ZenIcon;font-size:14px;font-style:normal;font-weight:400;font-variant:normal;line-height:1;text-align:center;text-transform:none;content:'\e603';opacity:.4;speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.picker-multi.picker-focus.picker-loading .picker-selections::before{display:inline-block;content:'\e97c';-webkit-animation:spin 2s infinite linear;-o-animation:spin 2s infinite linear;animation:spin 2s infinite linear}.picker-multi .picker-selection{position:relative;display:inline-block;padding:0 20px 0 5px;margin:2px;line-height:18px;background-color:#f1f1f1;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #ddd;border-radius:3px;-webkit-box-shadow:0 0 2px #fff inset,0 1px 0 rgba(0,0,0,.05);box-shadow:0 0 2px #fff inset,0 1px 0 rgba(0,0,0,.05)}.picker-selection-single{max-width:100%;padding:5px 20px 5px 8px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.picker-focus .picker-selection-single .picker-selection-text{display:none}.picker-selection-single:after{position:absolute;top:7px;right:0;display:block;width:24px;height:14px;font-family:ZenIcon;font-size:14px;font-style:normal;font-weight:400;font-variant:normal;line-height:1;text-align:center;text-transform:none;content:'\e6b8';opacity:.4;speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.picker-focus .picker-selection-single::after{content:'\e603'}.picker-focus.picker-loading .picker-selection-single::after{display:inline-block;content:'\e97c';-webkit-animation:spin 2s infinite linear;-o-animation:spin 2s infinite linear;animation:spin 2s infinite linear}.picker-selection-remove{position:absolute;top:0;right:0;width:20px;height:20px;text-align:center;cursor:pointer;opacity:.2}.picker-selection-remove:before{font-family:ZenIcon;font-size:14px;font-style:normal;font-weight:400;font-variant:normal;line-height:1;text-shadow:0 1px 0 #fff;text-transform:none;content:'\d7';speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.picker-selection-remove:hover{opacity:.5}.picker-single .picker-selection-remove{top:5px;right:20px;z-index:1}.picker-no-value .picker-selection-remove,.picker-single.picker-focus .picker-selection-remove{display:none}.picker-multi.picker-sortable .picker-selection-text{cursor:move}.picker-search{color:transparent;background:0 0;border:none}.picker-search:focus{color:inherit;outline:0}.picker-single .picker-search{position:absolute;top:0;right:0;bottom:0;left:0;z-index:1;width:100%;padding:5px 8px}.picker-multi .picker-search{display:inline-block;width:10px;min-width:10px;max-width:100%;padding:0 2px;margin:2px 0}.picker-placeholder{position:absolute;top:5px;right:20px;left:8px;z-index:0;display:none;overflow:hidden;color:grey;text-overflow:ellipsis;white-space:nowrap}.picker-no-value.picker-focus .picker-placeholder{display:none}.picker-input-empty.picker-focus.picker-no-value .picker-placeholder,.picker-no-value .picker-placeholder{display:block}.picker-drop-menu{position:fixed;z-index:1200;display:none;min-width:100px;background-color:#fff;border:1px solid rgba(0,0,0,.15);border-radius:4px;-webkit-box-shadow:0 6px 12px rgba(0,0,0,.12),0 1px 3px rgba(0,0,0,.1);box-shadow:0 6px 12px rgba(0,0,0,.12),0 1px 3px rgba(0,0,0,.1);opacity:0}.picker-drop-menu.picker-drop-show{display:block}.picker-drop-menu.picker-has-message{min-height:22px;padding-bottom:22px}.picker-option-list{padding:5px 0;margin:0;overflow-y:auto}.picker-no-options .picker-option-list{display:none}.picker-option{display:block;min-height:30px;padding:5px 10px;overflow:hidden;clear:both;font-weight:400;line-height:1.53846154;color:#353535;text-overflow:ellipsis;white-space:nowrap;-webkit-transition:none;-o-transition:none;transition:none}.picker-single .picker-option.picker-option-selected{color:#3280fc;background:#ebf2f9}.picker-option:focus,.picker-option:hover{text-decoration:none}.picker-option.picker-option-active,.picker-single .picker-option.picker-option-selected.picker-option-active{color:#fff;background-color:#3280fc}.picker-multi .picker-option.picker-option-selected,.picker-multi .picker-option.picker-option-selected.picker-option-active{color:#ccc;cursor:not-allowed;background-color:#fff}.picker-option-text-matched{position:relative;color:#3280fc}.picker-option-text-matched:before{position:absolute;right:0;bottom:-4px;left:0;display:block;height:2px;content:' ';background-color:#3280fc;border-radius:2px;opacity:.5}.picker-option-active .picker-option-text-matched,.picker-option:hover .picker-option-text-matched{color:#fff}.picker-option-active .picker-option-text-matched:before,.picker-option:hover .picker-option-text-matched:before{background-color:#fff}.picker-message{position:absolute;right:0;bottom:0;left:0;display:none;height:22px;padding:2px 5px;overflow:hidden;font-size:12px;color:grey;text-overflow:ellipsis;white-space:nowrap;background-color:#f1f1f1}.picker-has-message .picker-message{display:block}.picker-message[data-type=danger]{color:#ea644a}