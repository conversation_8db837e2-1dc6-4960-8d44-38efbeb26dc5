<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="zui/dist/css/zui.min.css" />
    <script src="zui/dist/lib/jquery/jquery-3.4.1.min.js"></script>
    <script src="zui/dist/js/zui.min.js"></script>
    <script src="common.js"></script>
    <title>物流网页首页</title>
</head>

<body>
    <nav class="navbar navbar-inverse" role="navigation">
        <div class="container-fluid">
            <!-- 导航头部 -->
            <div class="navbar-header">
                <!-- 移动设备上的导航切换按钮 -->
                <button type="button" class="navbar-toggle" data-toggle="collapse"
                    data-target=".navbar-collapse-example">
                    <span class="sr-only">切换导航</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <!-- 品牌名称或logo -->
                <a class="navbar-brand" href="#">顺丰物流</a>
            </div>
            <!-- 导航项目 -->
            <div class="collapse navbar-collapse" id="navbar-collapse-example">
                <!-- 左侧的导航项目 -->
                <ul class="nav navbar-nav navibar-left">
                    <li class="active"><a href="your/nice/url">项目</a></li>
                </ul>
                <!-- 右侧的导航项目 -->
                <ul class="nav navbar-nav navbar-right">
                    <li><a href="OrderServlet?action=send" id="send-btn">我要寄件</a></li>
                </ul>
                <ul class="nav navbar-nav navbar-right" id="loginInfo">
                    <li><a href="#" data-toggle="modal">登录/注册</a></li>
                </ul>
                <ul class="nav navbar-nav navbar-right" id="welcomeInfo">
                    <li id="userInfo"></li>
                </ul>
            </div><!-- END .navbar-collapse -->
        </div>
    </nav>
    <div class="container">
        <div class="panel panel-primary">
            <div class="panel-heading">
            </div>
            <div class="panel-body">
                <form class="form-horizontal" id="sendForm">
                    <div class="form-group">
                        <label class="col-sm-2">寄件人信息</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-4">
                                    <input type="text" id="senderPhone" class="form-control" placeholder="请输入手机号"
                                        required>
                                </div>
                                <div class="col-sm-4">
                                    <input type="text" id="senderName" class="form-control" placeholder="请输入姓名"
                                        required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-10 col-sm-offset-2">
                            <div class="row">
                                <div class="col-sm-12">
                                    <input type="text" id="senderAddress" class="form-control col-sm-12"
                                        placeholder="寄件人详细地址">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2">收件人信息</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-4">
                                    <input type="text" id="receiverName" class="form-control" placeholder="姓名" required>
                                </div>
                                <div class="col-sm-4">
                                    <input type="text" id="receiverPhone" class="form-control" placeholder="电话"
                                        required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-10 col-sm-offset-2">
                            <div class="row">
                                <div class="col-sm-12">
                                    <input type="text" id="receiverAddress" class="form-control col-sm-12"
                                        placeholder="收件人详细地址">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2">包裹分类</label>
                        <div class="col-sm-3">
                            <select id="parcelType" class="form-control" title="包裹类型" aria-label="包裹类型">
                                <option value="易碎物品">易碎物品</option>
                                <option value="贵重物品">贵重物品</option>
                                <option value="普通物品">普通物品</option>
                            </select>
                        </div>
                        <label class="col-sm-2">重量</label>
                        <div class="col-sm-3">
                            <input id="parcelWeight" type="number" min="0" step="0.1" class="form-control"
                                placeholder="以千克为单位">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2">费用</label>
                        <div class="col-sm-10">
                            <div class="input-group">
                                <span class="input-group-addon">￥</span>
                                <input id="price" type="number" min="0" step="0.1" name="price" class="form-control"
                                    placeholder="价格" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2">备注</label>
                        <div class="col-sm-10">
                            <textarea id="parcelRemark" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <button type="submit" class="btn btn-primary" title="提交寄件">提交寄件</button>
                            <button type="reset" class="btn" title="取消">取消</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 追踪信息模态窗口 -->
    <div class="modal fade" id="trackingModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">×</span><span class="sr-only">关闭</span></button>
                    <h4 class="modal-title">快递追踪信息</h4>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>状态</th>
                                <th>备注</th>
                                <th>更新时间</th>
                            </tr>
                        </thead>
                        <tbody id="trackingTable">
                            <!-- 追踪信息将动态插入这里 -->
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<script>
    $(function() {
        // 初始化通用功能（登录状态检查和登出功能）
        initCommonFeatures();
        
        const priceRules = {
            "易碎物品": { base: 15, rate: 3 },
            "贵重物品": { base: 20, rate: 5 },
            "普通物品": { base: 10, rate: 2 }
        }

        function calculatePrice() {
            const itemType = $('#parcelType').val();
            const weight = parseFloat($('#parcelWeight').val()) || 0;
            if (itemType && weight > 0) {
                const rule = priceRules[itemType];
                const price = rule.base + (weight * rule.rate);
                $('#price').val(price.toFixed(2));

            } else {
                $('#price').val(0);
            }
        }
        
        $('#sendForm').on('submit', function (e) {
            e.preventDefault();
            const senderPhone = $('#senderPhone').val();
            const senderName = $('#senderName').val();
            const senderAddress = $('#senderAddress').val();
            const receiverName = $('#receiverName').val();
            const receiverPhone = $('#receiverPhone').val();
            const receiverAddress = $('#receiverAddress').val();
            const itemType = $('#parcelType').val();
            const weight = $('#parcelWeight').val();
            const price = $('#price').val();
            const remark = $('#parcelRemark').val();
            $.post('OrderServlet', {
                action: 'receive',
                senderPhone: senderPhone,
                senderName: senderName,
                senderAddress: senderAddress,
                receiverName: receiverName,
                receiverPhone: receiverPhone,
                receiverAddress: receiverAddress,
                parcelType: itemType,
                weight: weight,
                price: price,
                remark: remark,
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString(),
            }, function (data) {
                if (data === "发货成功") {
                    var myMessagerq = new $.zui.Messager({ type: 'success' });
                    myMessagerq.show('寄件成功');
                } else {
                    var myMessagere = new $.zui.Messager({ type: 'danger' });
                    myMessagere.show('寄件失败');
                }
            }, "text")
        })
        
        $('#track-btn').click(function (){
            var parcelNo = $('#parcelNo').val();
            if (!parcelNo) {
                var myMessage = new $.zui.Messager({ type: 'warning' });
                myMessage.show('请输入快递单号');
                return;
            }
            // 清空之前的追踪信息
            $('#trackingTable').empty();
            // 显示模态窗口
            $('#trackingModal').modal('show');
            // 获取追踪信息
            showTracking(parcelNo);
        })
        
        function showTracking(parcelNo) {
            $.post('OrderServlet?action=showTracking&parcelNo=' + parcelNo, {}, function (data) {
                console.log("收到数据:", data); // 添加调试日志
                // 检查是否收到未登录消息
                if (typeof data === 'string' && data.includes('请先登录')) {
                    $('#trackingTable').append(`
                        <tr>
                            <td colspan="3" class="text-center">请先登录系统</td>
                        </tr>
                    `);
                    return;
                }
                
                if (!data || data.length === 0) {
                    $('#trackingTable').append(`
                        <tr>
                            <td colspan="3" class="text-center">未找到相关追踪信息</td>
                        </tr>
                    `);
                } else {
                    data.forEach(item => {
                        console.log("处理项目:", item); // 添加调试日志
                        
                        // 使用Tracking实体类的正确字段名
                        let status = item.trackingStatus || '未知状态';
                        let location = item.location || '-';
                        let time = item.trackingTime || '-';
                        
                        $('#trackingTable').append(`
                            <tr>
                                <td>${status}</td>
                                <td>${location}</td>
                                <td>${time}</td>
                            </tr>
                        `);
                    });
                }
            }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
                console.error("请求失败:", textStatus, errorThrown); // 添加错误日志
                console.log("响应文本:", jqXHR.responseText); // 查看响应内容
                
                // 检查是否是未登录错误
                if (jqXHR.responseText && jqXHR.responseText.includes('请先登录')) {
                    $('#trackingTable').append(`
                        <tr>
                            <td colspan="3" class="text-center">请先登录系统</td>
                        </tr>
                    `);
                } else {
                    $('#trackingTable').append(`
                        <tr>
                            <td colspan="3" class="text-center">获取追踪信息失败: ${textStatus}</td>
                        </tr>
                    `);
                }
            });
        }
        
        $('#parcelType, #parcelWeight').on('input change', calculatePrice);
    })
</script>