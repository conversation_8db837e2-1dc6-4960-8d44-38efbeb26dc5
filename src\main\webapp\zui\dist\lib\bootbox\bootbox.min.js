/*! bootbox.js v4.4.0 http://bootboxjs.com/license.txt */
!function(t,o){"use strict";"function"==typeof define&&define.amd?define(["jquery"],o):"object"==typeof exports?module.exports=o(require("jquery")):t.bootbox=o(t.jQuery)}(this,function t(o,e){"use strict";function a(t){var e=o.zui&&o.zui.getLangData?o.zui.getLangData("bootbox",d.locale,h):h[d.locale];return e?e[t]:h.en[t]}function n(t,o,e){t.stopPropagation(),t.preventDefault();var a="function"==typeof e&&e.call(o,t)===!1;a||o.modal("hide")}function r(t){var o,e=0;for(o in t)e++;return e}function l(t,e){var a=0;o.each(t,function(t,o){e(t,o,a++)})}function c(t){var e,a;if("object"!=typeof t)throw new Error("Please supply an object of options");if(!t.message)throw new Error("Please specify a message");return t=o.extend({},d,t),t.buttons||(t.buttons={}),e=t.buttons,a=r(e),l(e,function(t,n,r){if("function"==typeof n&&(n=e[t]={callback:n}),"object"!==o.type(n))throw new Error("button with key "+t+" must be an object");n.label||(n.label=t),n.className||(2===a&&("ok"===t||"confirm"===t)||1===a?n.className="btn-primary":n.className="btn-default")}),t}function i(t,o){var e=t.length,a={};if(e<1||e>2)throw new Error("Invalid argument length");return 2===e||"string"==typeof t[0]?(a[o[0]]=t[0],a[o[1]]=t[1]):a=t[0],a}function s(t,e,a){return o.extend(!0,{},t,i(e,a))}function u(t,o,e,a){var n={className:"bootbox-"+t,buttons:p.apply(null,o)};return b(s(n,a,e),o)}function p(){for(var t={},o=0,e=arguments.length;o<e;o++){var n=arguments[o],r=n.toLowerCase(),l=n.toUpperCase();t[r]={label:a(l)}}return t}function b(t,o){var a={};return l(o,function(t,o){a[o]=!0}),l(t.buttons,function(t){if(a[t]===e)throw new Error("button key "+t+" is not allowed (options are "+o.join("\n")+")")}),t}var f={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'></div></div></div></div></div>",header:"<div class='modal-header'><h4 class='modal-title'></h4></div>",footer:"<div class='modal-footer'></div>",closeButton:"<button type='button' class='bootbox-close-button close' data-dismiss='modal' aria-hidden='true'>&times;</button>",form:"<form class='bootbox-form'></form>",inputs:{text:"<input class='bootbox-input bootbox-input-text form-control' autocomplete=off type=text />",textarea:"<textarea class='bootbox-input bootbox-input-textarea form-control'></textarea>",email:"<input class='bootbox-input bootbox-input-email form-control' autocomplete='off' type='email' />",select:"<select class='bootbox-input bootbox-input-select form-control'></select>",checkbox:"<div class='checkbox'><label><input class='bootbox-input bootbox-input-checkbox' type='checkbox' /></label></div>",date:"<input class='bootbox-input bootbox-input-date form-control' autocomplete=off type='date' />",time:"<input class='bootbox-input bootbox-input-time form-control' autocomplete=off type='time' />",number:"<input class='bootbox-input bootbox-input-number form-control' autocomplete=off type='number' />",password:"<input class='bootbox-input bootbox-input-password form-control' autocomplete='off' type='password' />"}},d={locale:o.zui&&o.zui.clientLang?o.zui.clientLang():"en",backdrop:"static",animate:!0,className:null,closeButton:!0,show:!0,container:"body"},m={};m.alert=function(){var t;if(t=u("alert",["ok"],["message","callback"],arguments),t.callback&&"function"!=typeof t.callback)throw new Error("alert requires callback property to be a function when provided");return t.buttons.ok.callback=t.onEscape=function(){return"function"!=typeof t.callback||t.callback.call(this)},m.dialog(t)},m.confirm=function(){var t;if(t=u("confirm",["confirm","cancel"],["message","callback"],arguments),t.buttons.cancel.callback=t.onEscape=function(){return t.callback.call(this,!1)},t.buttons.confirm.callback=function(){return t.callback.call(this,!0)},"function"!=typeof t.callback)throw new Error("confirm requires a callback");return m.dialog(t)},m.prompt=function(){var t,a,n,r,c,i,u;if(r=o(f.form),a={className:"bootbox-prompt",buttons:p("cancel","confirm"),value:"",inputType:"text"},t=b(s(a,arguments,["title","callback"]),["confirm","cancel"]),i=t.show===e||t.show,t.message=r,t.buttons.cancel.callback=t.onEscape=function(){return t.callback.call(this,null)},t.buttons.confirm.callback=function(){var e;switch(t.inputType){case"text":case"textarea":case"email":case"select":case"date":case"time":case"number":case"password":e=c.val();break;case"checkbox":var a=c.find("input:checked");e=[],l(a,function(t,a){e.push(o(a).val())})}return t.callback.call(this,e)},t.show=!1,!t.title)throw new Error("prompt requires a title");if("function"!=typeof t.callback)throw new Error("prompt requires a callback");if(!f.inputs[t.inputType])throw new Error("invalid prompt type");switch(c=o(f.inputs[t.inputType]),t.inputType){case"text":case"textarea":case"email":case"date":case"time":case"number":case"password":c.val(t.value);break;case"select":var d={};if(u=t.inputOptions||[],!Array.isArray(u))throw new Error("Please pass an array of input options");if(!u.length)throw new Error("prompt with select requires options");l(u,function(t,a){var n=c;if(a.value===e||a.text===e)throw new Error("given options in wrong format");a.group&&(d[a.group]||(d[a.group]=o("<optgroup/>").attr("label",a.group)),n=d[a.group]),n.append("<option value='"+a.value+"'>"+a.text+"</option>")}),l(d,function(t,o){c.append(o)}),c.val(t.value);break;case"checkbox":var h=Array.isArray(t.value)?t.value:[t.value];if(u=t.inputOptions||[],!u.length)throw new Error("prompt with checkbox requires options");if(!u[0].value||!u[0].text)throw new Error("given options in wrong format");c=o("<div/>"),l(u,function(e,a){var n=o(f.inputs[t.inputType]);n.find("input").attr("value",a.value),n.find("label").append(a.text),l(h,function(t,o){o===a.value&&n.find("input").prop("checked",!0)}),c.append(n)})}return t.placeholder&&c.attr("placeholder",t.placeholder),t.pattern&&c.attr("pattern",t.pattern),t.maxlength&&c.attr("maxlength",t.maxlength),r.append(c),r.on("submit",function(t){t.preventDefault(),t.stopPropagation(),n.find(".btn-primary").click()}),n=m.dialog(t),n.off("shown.zui.modal"),n.on("shown.zui.modal",function(){c.focus()}),i===!0&&n.modal("show"),n},m.dialog=function(t){t=c(t);var a=o(f.dialog),r=a.find(".modal-dialog"),i=a.find(".modal-body"),s=t.buttons,u="",p={onEscape:t.onEscape};if(o.fn.modal===e)throw new Error("$.fn.modal is not defined; please double check you have included the Bootstrap JavaScript library. See http://getbootstrap.com/javascript/ for more details.");if(l(s,function(t,o){u+="<button data-bb-handler='"+t+"' type='button' class='btn "+o.className+"'>"+o.label+"</button>",p[t]=o.callback}),i.find(".bootbox-body").html(t.message),t.animate===!0&&a.addClass("fade"),t.className&&a.addClass(t.className),"large"===t.size?r.addClass("modal-lg"):"small"===t.size&&r.addClass("modal-sm"),t.title&&i.before(f.header),t.closeButton){var b=o(f.closeButton);t.title?a.find(".modal-header").prepend(b):b.css("margin-top","-10px").prependTo(i)}return t.title&&a.find(".modal-title").html(t.title),u.length&&(i.after(f.footer),a.find(".modal-footer").html(u)),a.on("hidden.zui.modal",function(t){t.target===this&&a.remove()}),a.on("shown.zui.modal",function(){a.find(".btn-primary:first").focus()}),"static"!==t.backdrop&&a.on("click.dismiss.zui.modal",function(t){a.children(".modal-backdrop").length&&(t.currentTarget=a.children(".modal-backdrop").get(0)),t.target===t.currentTarget&&a.trigger("escape.close.bb")}),a.on("escape.close.bb",function(t){p.onEscape&&n(t,a,p.onEscape)}),a.on("click",".modal-footer button",function(t){var e=o(this).data("bb-handler");n(t,a,p[e])}),a.on("click",".bootbox-close-button",function(t){n(t,a,p.onEscape)}),a.on("keyup",function(t){27===t.which&&a.trigger("escape.close.bb")}),o(t.container).append(a),a.modal({backdrop:!!t.backdrop&&"static",keyboard:!1,show:!1}),t.show&&a.modal("show"),a},m.setDefaults=function(){var t={};2===arguments.length?t[arguments[0]]=arguments[1]:t=arguments[0],o.extend(d,t)},m.hideAll=function(){return o(".bootbox").modal("hide"),m};var h={en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"Confirm"},zh_cn:{OK:"确认",CANCEL:"取消",CONFIRM:"确认"},zh_tw:{OK:"確認",CANCEL:"取消",CONFIRM:"確認"}};return m.addLocale=function(t,e){return o.each(["OK","CANCEL","CONFIRM"],function(t,o){if(!e[o])throw new Error("Please supply a translation for '"+o+"'")}),h[t]={OK:e.OK,CANCEL:e.CANCEL,CONFIRM:e.CONFIRM},m},m.removeLocale=function(t){return delete h[t],m},m.setLocale=function(t){return m.setDefaults("locale",t)},m.init=function(e){return t(e||o)},m});