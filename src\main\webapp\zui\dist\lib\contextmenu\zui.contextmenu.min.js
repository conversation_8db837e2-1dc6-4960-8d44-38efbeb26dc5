/*!
 * ZUI: 右键菜单 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
!function(t,e){"use strict";var n="zui.contextmenu",o={animation:"fade",menuTemplate:'<ul class="dropdown-menu"></ul>',toggleTrigger:!1,duration:200,limitInsideWindow:!0},i=!1,r={},a="zui-contextmenu-"+t.zui.uuid(),s=0,u=0,l=function(){return t(document).off("mousemove."+n).on("mousemove."+n,function(t){s=t.clientX,u=t.clientY}),r},d=function(e,n){if("string"==typeof e&&(e="seperator"===e||"divider"===e||"-"===e||"|"===e?{type:"seperator"}:{label:e,id:n}),"seperator"===e.type||"divider"===e.type)return t('<li class="divider"></li>');var o=t("<a/>").attr(t.extend({href:e.url||"###","class":e.className,style:e.style},e.attrs)).data("item",e);return e.html?e.html===!0?o.html(e.label||e.text):o=t(e.html):o.text(e.label||e.text),e.icon&&o.prepend('<i class="icon icon-'+e.icon+'"></i>'),e.onClick&&o.on("click",e.onClick),t("<li />").toggleClass("disabled",e.disabled===!0).append(o)},c=function(e){var n=t("#"+a);return n.length&&n.hasClass("contextmenu-show")&&(!e||(n.data("options")||{}).id===e)},h=null,m=function(e,n){"function"==typeof e&&(n=e,e=null),h&&(clearTimeout(h),h=null);var o=t("#"+a);if(o.length){var i=o.removeClass("contextmenu-show").data("options");if(!e||i.id===e){var s=function(){o.find(".contextmenu-menu").removeClass("open"),i.onHidden&&i.onHidden(),n&&n()};i.onHide&&i.onHide();var u=i.animation;o.find(".contextmenu-menu").removeClass("in"),u?h=setTimeout(s,i.duration):s()}}return r},f=function(l,c,f){t.isPlainObject(l)&&(f=c,c=l,l=c.items),i=!0,c=t.extend({},o,c);var p=t("#"+a);p.length||(p=t('<div style="position: fixed; z-index: 2000;" class="contextmenu" id="'+a+'"><div class="contextmenu-menu"></div></div>').appendTo("body"));var g=p.find(".contextmenu-menu").off("click."+n).on("click."+n,"a,.contextmenu-item",function(e){var n=t(this),o=c.onClickItem&&c.onClickItem(n.data("item"),n,e,c);o!==!1&&m()}).empty();g.attr("class","contextmenu-menu"+(c.className?" "+c.className:"")),p.attr("class","contextmenu contextmenu-show");var v=c.menuCreator;if(v)g.append(v(l,c));else{g.append(c.menuTemplate);var x=g.children().first(),w=c.itemCreator||d,y=typeof l;if("string"===y?l=l.split(","):"function"===y&&(l=l(c)),!l)return!1;t.each(l,function(t,e){x.append(w(e,t,c))})}var C=c.animation,b=c.duration;C===!0&&(c.animation=C="fade"),h&&(clearTimeout(h),h=null);var T=function(){g.addClass("in"),c.onShown&&c.onShown(),f&&f()};c.onShow&&c.onShow(),p.data("options",{animation:C,onHide:c.onHide,onHidden:c.onHidden,id:c.id,duration:b});var M=c.x,k=c.y;M===e&&(M=(c.event||c).clientX),M===e&&(M=s),k===e&&(k=(c.event||c).clientY),k===e&&(k=u);var x=g.children().first(),$=x.outerWidth(),H=x.outerHeight();if(c.position){var S=c.position({x:M,y:k,width:$,height:H},c,g);S&&(M=S.x,k=S.y)}if(c.limitInsideWindow){var z=t(window);M=Math.max(0,Math.min(M,z.width()-$)),k=Math.max(0,Math.min(k,z.height()-H))}return p.css({left:M,top:k}).show(),g.addClass("open"),C?(g.addClass(C),h=setTimeout(function(){T(),i=!1},10)):(T(),i=!1),r};t.extend(r,{NAME:n,DEFAULTS:o,show:f,hide:m,listenMouse:l,isShow:c}),t.zui({ContextMenu:r});var p=function(e,o){var i=this;i.name=n,i.$=t(e),i.id=t.zui.uuid(),o=i.options=t.extend({trigger:"contextmenu"},r.DEFAULTS,this.$.data(),o);var a=function(t){if("mousedown"!==t.type||2===t.button){if(o.toggleTrigger&&i.isShow())i.hide();else{var e={x:t.clientX,y:t.clientY,event:t};if(i.show(e)===!1)return}return t.preventDefault(),t.returnValue=!1,!1}},s=o.trigger,u=s+"."+n;o.selector?i.$.on(u,o.selector,a):i.$.on(u,a),o.show&&i.show("object"==typeof o.show?o.show:null)};p.prototype.destory=function(){that.$.off("."+n)},p.prototype.hide=function(t){return r.hide(this.id,t)},p.prototype.show=function(e,n){return e=t.extend({id:this.id,$toggle:this.$},this.options,e),r.show(e,n)},p.prototype.isShow=function(){return c(this.id)},t.fn.contextmenu=function(e){return this.each(function(){var o=t(this),i=o.data(n),r="object"==typeof e&&e;i||o.data(n,i=new p(this,r)),"string"==typeof e&&i[e]()})},t.fn.contextmenu.Constructor=p,t.fn.contextDropdown=function(e){t(this).contextmenu(t.extend({trigger:"click",animation:"fade",toggleTrigger:!0,menuCreator:function(e,n){var o=n.$toggle,i=o.attr("data-target");i||(i=o.attr("href"),i=i&&/#/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var r=i?t(i):o.next(".dropdown-menu"),a=n.transferEvent;if(a!==!1){var s="data-contextmenu-index";r.find("a,.contextmenu-item").each(function(e){t(this).attr(s,e)});var u=r.clone();return u.on("string"==typeof a?a:"click","a,.contextmenu-item",function(e){var n=r.find("["+s+'="'+t(this).attr(s)+'"]'),o=n[0];if(o)return o[e.type]?o[e.type]():n.trigger(e.type),e.preventDefault(),e.stopPropagation(),!1}),u}return r.clone()},position:function(t,e,n){var o=e.placement,i=e.$toggle;if(!o){var r=n.find(".dropdown-menu"),a=r.hasClass("pull-right"),s=i.parent().hasClass("dropup");o=a?s?"top-right":"bottom-right":s?"top-left":"bottom-left",a&&r.removeClass("pull-right")}var u=i[0].getBoundingClientRect();switch(o){case"top-left":return{x:u.left,y:Math.floor(u.top-t.height)};case"top-right":return{x:Math.floor(u.right-t.width),y:Math.floor(u.top-t.height)};case"bottom-left":return{x:u.left,y:u.bottom};case"bottom-right":return{x:Math.floor(u.right-t.width),y:u.bottom}}return t}},e))},t(document).on("click",function(e){var o=t(e.target),r=o.closest('[data-toggle="context-dropdown"]');if(r.length){var a=r.data(n);a||r.contextDropdown({show:!0})}else i||o.closest(".contextmenu").length||m()})}(jQuery,void 0);