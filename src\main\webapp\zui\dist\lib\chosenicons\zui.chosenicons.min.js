/*!
 * ZUI: 图标选择器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */
+function(o){"use strict";var e="zui.chosenIcons",t=function(e,n){this.$=o(e),this.options=this.getOptions(n),this.lang=t.LANGS[this.options.lang]||t.LANGS.en,this.id="chosen-icons-"+o.zui.uuid(),this.init()};t.DEFAULTS={optional:!0,lang:"zh_cn",icons:{common:["heart","user","group","list-ul","th","th-large","star","star-empty","search","envelope","dashboard","sitemap","umbrella","lightbulb","envelope-alt","cog","ok","remove","home","time","flag","flag-alt","flag-checkered","qrcode","tag","tags","book","bookmark","bookmark-empty","print","camera","picture","globe","map-marker","edit","edit-sign","play","stop","plus-sign","minus-sign","remove-sign","ok-sign","check-sign","question-sign","info-sign","exclamation-sign","plus","plus-sign","minus","minus-sign","asterisk","calendar","calendar-empty","comment","comment-alt","comments","comments-alt","folder-close","folder-open","folder-close-alt","folder-open-alt","thumbs-up","thumbs-down","pushpin","building","phone","rss","rss-sign","bullhorn","bell","bell-alt","certificate","wrench","tasks","cloud","beaker","magic","smile","frown","meh","code","location-arrow"],web:["share","pencil","trash","file-alt","file","file-text","download-alt","upload-alt","inbox","repeat","refresh","lock","check","check-empty","eye-open","eye-close","key","signin","signout","external-link","external-link-sign","link","reorder","quote-left","quote-right","spinner","reply","question","info","archive","collapse","collapse-top"],editor:["table","copy","save","list-ol","paste","keyboard","paper-clip","crop","unlink","sort-by-alphabet","sort-by-alphabet-alt","sort-by-attributes","sort-by-attributes-alt","sort-by-order","sort-by-order-alt"],directional:["chevron-left","chevron-right","chevron-down","chevron-up","arrow-left","arrow-right","arrow-down","arrow-up","hand-right","hand-left","hand-up","hand-down","circle-arrow-left","circle-arrow-right","circle-arrow-up","circle-arrow-down","double-angle-left","double-angle-right","double-angle-down","double-angle-up","angle-left","angle-right","angle-down","angle-up","long-arrow-left","long-arrow-right","long-arrow-down","long-arrow-up","caret-left","caret-right","caret-down","caret-up"],other:["desktop","laptop","tablet","mobile","building","firefox","ie","opera","qq","lemon","sign-blank","circle","circle-blank","terminal","html5","android","apple","windows","weibo","wechat","renren","bug","moon","sun"]}},t.LANGS={},t.LANGS.zh_cn={emptyIcon:"[没有图标]",commonIcons:"常用图标",webIcons:"Web 图标",editorIcons:"编辑器图标",directionalIcons:"箭头总汇",otherIcons:"其他图标"},t.LANGS.en={emptyIcon:"[No Icon]",commonIcons:"Common Icons",webIcons:"Web Icons",editorIcons:"Editor Icons",directionalIcons:"Directional Icons",otherIcons:"Other Icons"},t.LANGS.zh_tw={emptyIcon:"[沒有圖標]",commonIcons:"常用圖標",webIcons:"Web 圖標",editorIcons:"編輯器圖標",directionalIcons:"箭頭總匯",otherIcons:"其他圖標"},t.prototype.getOptions=function(e){return e=o.extend(!0,{placeholder_text:" ",disable_search:!0,width:"100%",inherit_select_classes:!0},t.DEFAULTS,this.$.data(),e)},t.prototype.init=function(){var e=this,t=this.$.addClass("chosen-icons").addClass(this.id).removeClass("form-control");t.empty(),this.options.optional&&t.append(this.getOptionHtml());var n=this.lang,i=[];o.each(this.options.icons,function(o,t){i.push(e.getgroupHtml(o,t))}),t.append(i.join("")),t.chosen(this.options);var s=".chosen-container."+this.id;t.on("chosen:showing_dropdown",function(){o(s+" .chosen-results .group-option").each(function(){var e=o(this).addClass("icon"),t=o(this).text();e.html('<i class="icon-'+t+'" title="'+t+'"></i>')})}).change(function(){var e=o(s+" .chosen-single > span"),t=o(this).val();t&&t.length>0?e.html('<i class="'+t+'"></i> &nbsp; <span class="text-muted">'+t.substr(5).replace(/-/g," ")+"</span>"):e.html('<span class="text-muted">'+n.emptyIcon+"</span>")});var r=t.data("value");r&&t.val(r).change()},t.prototype.getgroupHtml=function(e,t){t=t||this.options.icons[e];var n=[],i=this;return o.each(t,function(o,e){n.push(i.getOptionHtml(e))}),'<optgroup label="'+this.lang[e+"Icons"]+'">'+n.join("")+"</optgroup>"},t.prototype.getOptionHtml=function(o){var e=o;return o&&o.length>0?o="icon-"+o:(o="",e=this.lang.emptyIcon),'<option value="'+o+'">'+e+"</option>"},o.fn.chosenIcons=function(n){return this.each(function(){var i=o(this),s=i.data(e),r="object"==typeof n&&n;s||i.data(e,s=new t(this,r)),"string"==typeof n&&s[n]()})},o.fn.chosenIcons.Constructor=t}(jQuery);