package com.ruoyi.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.ruoyi.dao.TrackDao;
import com.ruoyi.entity.Tracking;
import com.ruoyi.jdbc.JdbcUtil;

public class TrackDaoImpl implements TrackDao {
    @Override
    public List<Tracking> getTrackByParcelNo(String parcelNo) throws SQLException, ClassNotFoundException {
        String sql = "select * from t_tracking where parcel_id = ?";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Tracking> trackList = new ArrayList<>();
        
        try {
            conn = JdbcUtil.getConnection();
            ps = conn.prepareStatement(sql);
            ps.setString(1, parcelNo);
            rs = ps.executeQuery();
            
            while (rs.next()) {
                Tracking track = new Tracking();
                track.setLocation(rs.getString("location"));
                track.setTrackingStatus(rs.getString("tracking_status"));
                track.setTrackingTime(rs.getString("tracking_time"));
                track.setPracelId(rs.getInt("parcel_id"));
                trackList.add(track);
            }
        } finally {
            // 关闭资源
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
            if (conn != null) {
                conn.close();
            }
        }
        
        return trackList.isEmpty() ? new ArrayList<>() : trackList;
    }
}

