package com.ruoyi.jdbc;

import java.sql.*;

public class JdbcUtil {
    public static Connection getConnection() throws ClassNotFoundException, SQLException {
        Class.forName("com.mysql.cj.jdbc.Driver");
        String url="********************************";
        String user="root";
        String password="123456";
        Connection conn= DriverManager.getConnection(url, user, password);
        return conn;
    }
    public static void close(Connection conn,Statement statement,ResultSet resultSet) throws SQLException {
        if(resultSet!=null){
            resultSet.close();
        }
        if(statement!=null){
            statement.close();
        }
        if(conn!=null){
            conn.close();
        }
    }
}
