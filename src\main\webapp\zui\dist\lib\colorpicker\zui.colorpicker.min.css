/*!
 * ZUI: 颜色选择器 - v1.10.0 - 2021-11-04
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2021 cnezsoft.com; Licensed MIT
 */.colorpicker .dropdown-menu{min-width:0;padding:2px}.colorpicker .dropdown-menu>li{display:block;float:left;padding:2px}.colorpicker .dropdown-menu>li>a{position:relative;display:block;width:100%;height:100%;padding:0;font-family:ZenIcon;font-size:14px;font-style:normal;font-weight:400;font-variant:normal;line-height:1;text-align:center;text-transform:none;border:1px solid transparent;border-radius:4px;speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.colorpicker .dropdown-menu>li>a:before{position:absolute;top:50%;display:block;width:100%;height:20px;margin-top:-8px}.colorpicker .dropdown-menu>li>a:hover{border-color:#333;-webkit-box-shadow:0 1px 4px rgba(0,0,0,.25);box-shadow:0 1px 4px rgba(0,0,0,.25)}.colorpicker .dropdown-menu>li>a.active:before{content:'\e60d'}.colorpicker .dropdown-menu>li>a.empty{color:#666;background:#f2f2f2}.colorpicker .dropdown-menu>li>a.empty:before{content:'\d7'}.colorpicker .btn{text-shadow:none}.colorpicker .btn .cp-title{display:inline-block;margin-right:5px}.colorpicker.btn-wrapper{position:relative;display:inline-block}